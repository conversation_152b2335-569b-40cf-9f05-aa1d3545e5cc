# Git 拉取最新提交恢复项目指南

当项目被意外修改或搞崩时，可以通过以下步骤恢复到远程仓库的最新状态。

## 完整流程

### 1. 检查当前状态（可选）
```bash
git status
```
这一步可以查看当前有哪些文件被修改，了解项目的当前状态。

### 2. 拉取远程最新提交
```bash
git fetch origin
```
从远程仓库获取最新的提交信息，但不会自动合并到本地分支。

### 3. 强制重置到远程最新状态
```bash
git reset --hard origin/main
```
**注意：这个命令会丢弃所有本地未提交的更改！**

- `--hard` 参数会重置工作目录和暂存区
- `origin/main` 是远程主分支的引用
- 执行后，本地代码会完全匹配远程仓库的最新状态

### 4. 验证恢复结果（可选）
```bash
git status
```
确认项目已经恢复到干净状态。

## 一键恢复命令

如果你确定要丢弃所有本地更改，可以直接执行：
```bash
git fetch origin && git reset --hard origin/main
```

## 重要提醒

⚠️ **使用 `git reset --hard` 前请确认：**
- 所有重要的本地更改已经提交或备份
- 你确实想要丢弃所有未提交的更改
- 后端的 static 目录内容通常会被保留（如果它们已经在远程仓库中）

## 其他分支

如果你在其他分支工作，将命令中的 `main` 替换为对应的分支名：
```bash
git reset --hard origin/你的分支名
```