<template>
	<!-- 充值弹窗 -->
	<view v-if="modelValue" class="recharge-popup">
		<view class="recharge-content">
			<view class="recharge-header">
				<text class="recharge-title">哇图币充值</text>
				<view class="recharge-close" @click="closeModal">×</view>
			</view>
			<view class="recharge-description">
				选择充值金额，获得对应哇图币
			</view>
			<view class="recharge-options">
				<view 
					v-for="(option, index) in rechargeOptions" 
					:key="index" 
					class="recharge-option" 
					:class="{ 'option-selected': selectedOption === index }"
					@click="selectOption(index)">
					<view class="option-content">
						<view class="option-left">
							<view class="option-coin-row">
								<view class="option-coin-amount">
									<image class="option-coin-icon" src="/static/coins.png"></image>
									<text>{{ option.coins }}</text>
								</view>
								<view class="option-generate-count">
									最多可生成{{ Math.round(option.coins / 30) }}图
								</view>
							</view>
						</view>
						<view class="option-price">¥{{ option.price }}</view>
					</view>
				</view>
			</view>
			<view class="recharge-action">
				<view class="recharge-button" @click="confirmRecharge">立即充值</view>
			</view>
		</view>
	</view>
	
	<!-- 充值成功动画 -->
	<view v-if="showRechargeSuccess" class="recharge-success-popup">
		<view class="recharge-success-content">
			<view class="success-icon"></view>
			<view class="success-title">充值成功</view>
			<view class="success-reward">+{{rechargeAmount}} 哇图币</view>
			
			<!-- 金币掉落动画元素 -->
			<view v-for="i in 12" :key="i" class="coin-rain" :class="`coin-rain-${i}`"></view>
		</view>
	</view>
</template>

<script>
	import request from '../utils/request.js'
	
	export default {
		name: 'RechargeModal',
		props: {
			modelValue: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		emits: ['update:modelValue', 'recharge-success'],
		data() {
			return {
				rechargeOptions: [], // 充值选项数组
				selectedOption: -1, // 当前选中的充值选项索引
				showRechargeSuccess: false, // 控制充值成功弹窗显示
				rechargeAmount: 0, // 充值成功的金额
			}
		},
		watch: {
			modelValue: {
				handler(val) {
					if (val) {
						this.loadRechargeOptions();
					}
				},
				immediate: true
			}
		},
		methods: {
			// 关闭弹窗
			closeModal() {
				this.$emit('update:modelValue', false);
			},
			
			// 加载充值选项
			async loadRechargeOptions() {
				try {
					// 动态获取可用充值档位
					const options = await request.get('/wowpic/pay/options')
					if (!options || options.length === 0) {
						uni.showToast({ title: '暂无充值选项', icon: 'none' })
						this.closeModal();
						return
					}
					
					// 后端返回字段 price_cny，需要转换为 price 便于前端统一展示
					this.rechargeOptions = options.map(o => ({
						coins: o.coins,
						price: o.price_cny
					}))
					
					this.selectedOption = 0 // 默认选中第一个选项
				} catch (e) {
					console.error('获取充值选项失败', e)
					uni.showToast({ title: '获取充值选项失败', icon: 'none' })
					this.closeModal();
				}
			},
			
			// 选择充值选项
			selectOption(index) {
				this.selectedOption = index
			},
			
			// 确认充值
			async confirmRecharge() {
				// 检查是否选择了充值选项
				if (this.selectedOption === -1 || !this.rechargeOptions[this.selectedOption]) {
					uni.showToast({ title: '请选择充值金额', icon: 'none' })
					return
				}
				
				try {
					const selectedOption = this.rechargeOptions[this.selectedOption]
					const selectedAmount = selectedOption.coins
					
					// 向后端申请充值订单
					const orderRes = await request.post('/wowpic/pay/recharge', { amount: selectedAmount })
					
					if (!orderRes || !orderRes.success) {
						uni.showToast({ title: '创建订单失败', icon: 'none' })
						return
					}
					
					const payParams = orderRes.payment_params || {}
					
					// 添加加载中提示
					uni.showLoading({
						title: '处理中...',
						mask: true
					})
					
					// 调用微信小程序支付接口
					uni.requestPayment({
						...payParams,
						success: () => {
							// 隐藏加载提示
							uni.hideLoading()
							
							// 关闭充值弹窗
							this.closeModal();
							
							// 保存充值金额用于显示
							this.rechargeAmount = selectedAmount
							
							// 显示自定义充值成功动画
							this.showRechargeSuccess = true
							
							// 3秒后隐藏成功动画
							setTimeout(() => {
								this.showRechargeSuccess = false
							}, 3000)
							
							// 通知父组件充值成功，需要刷新用户信息
							this.$emit('recharge-success', selectedAmount);
						},
						fail: (err) => {
							// 隐藏加载提示
							uni.hideLoading()
							
							console.error('支付失败', err)
							// 判断是取消支付还是其他错误
							if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
								uni.showToast({ title: '支付已取消', icon: 'none' })
							} else {
								uni.showModal({
									title: '支付提示',
									content: '支付遇到问题，请稍后再试或联系客服',
									showCancel: false
								})
							}
						}
					})
				} catch (e) {
					uni.hideLoading()
					console.error('充值处理出错', e)
				}
			}
		}
	}
</script>

<style>
	/* 充值弹窗样式 */
	.recharge-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
		animation: fadeIn 0.3s ease;
	}

	.recharge-content {
		width: 85%;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		border: 6rpx solid #333;
		overflow: hidden;
		box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.2);
		position: relative;
	}

	.recharge-content::before {
		content: "";
		position: absolute;
		top: -20rpx;
		right: -20rpx;
		width: 120rpx;
		height: 120rpx;
		background: linear-gradient(135deg, rgba(255, 149, 0, 0.2) 0%, rgba(255, 149, 0, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}

	.recharge-content::after {
		content: "";
		position: absolute;
		bottom: -20rpx;
		left: -20rpx;
		width: 120rpx;
		height: 120rpx;
		background: linear-gradient(135deg, rgba(78, 103, 235, 0.2) 0%, rgba(78, 103, 235, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}

	.recharge-header {
		background: linear-gradient(135deg, #4A90E2 0%, #7562FF 100%);
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: relative;
		z-index: 1;
	}

	.recharge-title {
		color: #FFFFFF;
		font-size: 36rpx;
		font-weight: bold;
		text-shadow: 2rpx 2rpx 0 rgba(0, 0, 0, 0.3);
	}

	.recharge-close {
		width: 56rpx;
		height: 56rpx;
		line-height: 48rpx;
		text-align: center;
		font-size: 48rpx;
		color: #FFFFFF;
		background-color: rgba(0, 0, 0, 0.2);
		border-radius: 50%;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
	}

	.recharge-description {
		text-align: center;
		color: #999;
		font-size: 28rpx;
		padding: 20rpx 30rpx 10rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.recharge-options {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 30rpx;
	}

	.recharge-option {
		width: 100%; /* 改为占满整行 */
		margin-bottom: 20rpx;
		border: 4rpx solid #E0E0E0;
		border-radius: 16rpx;
		padding: 24rpx;
		position: relative;
		transition: all 0.3s ease;
	}

	.recharge-option:active {
		transform: scale(0.98);
	}

	.option-content {
		display: flex;
		align-items: center;
		justify-content: space-between; /* 使内容两端对齐 */
	}

	.option-left {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.option-coin-row {
		display: flex;
		align-items: center;
		flex-wrap: nowrap;
	}

	.option-coin-amount {
		display: flex;
		align-items: center;
		margin-right: 10rpx;
		margin-bottom: 0;
	}

	.option-generate-count {
		font-size: 24rpx;
		color: #FF9500;
		font-weight: bold;
		background-color: rgba(255, 149, 0, 0.1);
		padding: 2rpx 8rpx;
		border-radius: 10rpx;
		border: 1px dashed #FF9500;
		white-space: nowrap;
	}

	.option-coin-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 8rpx;
	}

	.option-coin-amount text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.option-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #FF9500;
	}

	.recharge-action {
		padding: 0rpx 30rpx 40rpx;
		display: flex;
		justify-content: center;
	}

	.recharge-button {
		background: #FF9500;
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: bold;
		padding: 20rpx 0;
		border-radius: 50rpx;
		width: 80%;
		text-align: center;
		box-shadow: 0 8rpx 16rpx rgba(255, 149, 0, 0.3);
		border: 4rpx solid rgba(0, 0, 0, 0.1);
		position: relative;
		overflow: hidden;
	}

	/* 充值成功动画 */
	.recharge-success-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
		animation: fadeIn 0.3s ease;
	}

	.recharge-success-content {
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 50rpx 60rpx;
		text-align: center;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
		animation: popIn 0.5s ease;
		max-width: 70%;
		position: relative;
		overflow: hidden;
	}

	.recharge-success-content::before {
		content: "";
		position: absolute;
		top: -30rpx;
		left: -30rpx;
		width: 150rpx;
		height: 150rpx;
		background: linear-gradient(135deg, rgba(255, 149, 0, 0.2) 0%, rgba(255, 149, 0, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}

	.recharge-success-content::after {
		content: "";
		position: absolute;
		bottom: -30rpx;
		right: -30rpx;
		width: 150rpx;
		height: 150rpx;
		background: linear-gradient(135deg, rgba(82, 196, 26, 0.2) 0%, rgba(82, 196, 26, 0) 70%);
		border-radius: 50%;
		z-index: 0;
	}

	@keyframes fadeIn {
		0% {
			opacity: 0;
		}
		100% {
			opacity: 1;
		}
	}

	@keyframes popIn {
		0% {
			transform: scale(0.8);
			opacity: 0;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.success-icon {
		width: 120rpx;
		height: 120rpx;
		margin: 0 auto 30rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2352C41A'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		position: relative;
		z-index: 1;
	}

	.success-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		position: relative;
		z-index: 1;
	}

	.success-reward {
		font-size: 48rpx;
		font-weight: bold;
		color: #FF9500;
		margin-bottom: 20rpx;
		text-shadow: 0 2rpx 4rpx rgba(255, 149, 0, 0.2);
		position: relative;
		z-index: 1;
		animation: bounceIn 0.8s ease 0.2s both;
	}

	@keyframes bounceIn {
		0% {
			transform: scale(0.3);
			opacity: 0;
		}
		50% {
			transform: scale(1.2);
		}
		70% {
			transform: scale(0.9);
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* 金币动画效果 */
	.coin-rain {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFD700'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		z-index: 2;
		opacity: 0;
	}

	.coin-rain-1 {
		top: 10%;
		left: 20%;
		animation: coinRain 2s ease-in 0.1s forwards;
	}

	.coin-rain-2 {
		top: 20%;
		left: 40%;
		animation: coinRain 2s ease-in 0.2s forwards;
	}

	.coin-rain-3 {
		top: 30%;
		left: 60%;
		animation: coinRain 2s ease-in 0.3s forwards;
	}

	.coin-rain-4 {
		top: 40%;
		left: 80%;
		animation: coinRain 2s ease-in 0.4s forwards;
	}

	.coin-rain-5 {
		top: 50%;
		left: 10%;
		animation: coinRain 2s ease-in 0.5s forwards;
	}

	.coin-rain-6 {
		top: 60%;
		left: 30%;
		animation: coinRain 2s ease-in 0.6s forwards;
	}

	.coin-rain-7 {
		top: 70%;
		left: 50%;
		animation: coinRain 2s ease-in 0.7s forwards;
	}

	.coin-rain-8 {
		top: 80%;
		left: 70%;
		animation: coinRain 2s ease-in 0.8s forwards;
	}

	.coin-rain-9 {
		top: 90%;
		left: 90%;
		animation: coinRain 2s ease-in 0.9s forwards;
	}

	.coin-rain-10 {
		top: 10%;
		right: 20%;
		animation: coinRain 2s ease-in 1s forwards;
	}

	.coin-rain-11 {
		top: 20%;
		right: 40%;
		animation: coinRain 2s ease-in 1.1s forwards;
	}

	.coin-rain-12 {
		top: 30%;
		right: 60%;
		animation: coinRain 2s ease-in 1.2s forwards;
	}

	@keyframes coinRain {
		0% {
			transform: translateY(-100rpx) rotate(0deg);
			opacity: 1;
		}
		50% {
			opacity: 1;
		}
		100% {
			transform: translateY(300rpx) rotate(360deg);
			opacity: 0;
		}
	}

	/* 选中状态样式 */
	.option-selected {
		border-color: #7562FF;
		background-color: #F5F6FF;
		box-shadow: 0 4rpx 12rpx rgba(117, 98, 255, 0.2);
	}

	.option-selected::before {
		content: "";
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		background-color: #7562FF;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/%3E%3C/svg%3E");
		background-size: 60%;
		background-repeat: no-repeat;
		background-position: center;
		border: 2rpx solid #FFFFFF;
	}
</style> 