from abc import ABC, abstractmethod
from typing import Dict, List

class BaseStyle(ABC):
    """所有风格的抽象基类"""

    @abstractmethod
    async def generate(self, params: Dict) -> List[str]:
        """生成图片核心方法，返回一个或多个图片URL列表
        
        Args:
            params: 生成参数，包含prompt、source_image_url、n（生成数量）等
            
        Returns:
            List[str]: 生成的图片URL列表
        """
        raise NotImplementedError

    @abstractmethod
    def validate_params(self, params: Dict) -> bool:
        """验证输入参数"""
        raise NotImplementedError

    @property
    @abstractmethod
    def name(self) -> str:
        """风格名称标识"""
        raise NotImplementedError 