from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker
import pymysql
import logging
from config import Config

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        
    def create_database_if_not_exists(self):
        """检查并创建数据库"""
        try:
            # 连接MySQL服务器（不指定数据库）
            server_url = f"mysql+pymysql://{Config.DB_USER}:{Config.DB_PASSWORD}@{Config.DB_HOST}:{Config.DB_PORT}/?charset=utf8mb4"
            temp_engine = create_engine(server_url)

            with temp_engine.connect() as conn:
                # 设置会话时区为北京时间
                conn.execute(text("SET time_zone = '+08:00'"))

                # 检查数据库是否存在
                result = conn.execute(text(f"SHOW DATABASES LIKE '{Config.DB_NAME}'"))
                if not result.fetchone():
                    # 创建数据库
                    conn.execute(text(f"CREATE DATABASE {Config.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
                    logger.info(f"✓ 创建数据库: {Config.DB_NAME}")
                else:
                    logger.info(f"✓ 数据库已存在: {Config.DB_NAME}")

            temp_engine.dispose()

        except Exception as e:
            logger.error(f"创建数据库失败: {e}")
            raise
    
    def init_database(self):
        """初始化数据库连接"""
        try:
            # 确保数据库存在
            self.create_database_if_not_exists()
            
            # 创建引擎
            self.engine = create_engine(
                Config.DATABASE_URL,
                pool_pre_ping=True,
                pool_recycle=300,
                echo=False  # 禁用SQL日志
            )

            # 添加事件监听器，每次连接时自动设置时区为北京时间
            @event.listens_for(self.engine, "connect")
            def set_timezone(dbapi_connection, connection_record):
                with dbapi_connection.cursor() as cursor:
                    cursor.execute("SET time_zone = '+08:00'")

            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            logger.info("✓ 数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    def get_db(self):
        """获取数据库会话"""
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

# 全局数据库管理器实例
db_manager = DatabaseManager()