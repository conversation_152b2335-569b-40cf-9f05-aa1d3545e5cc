import os
import logging
import uuid
from pathlib import Path
from typing import List, Optional
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, status, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from config import Config
from utils.auth import get_current_user, get_db
from database.models import User

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/wowpic/upload",
    tags=["文件上传"],
)

# 确保上传目录存在
def ensure_upload_dir():
    """确保上传目录存在，如果不存在则创建"""
    upload_dir = Path("static/uploads")
    upload_dir.mkdir(parents=True, exist_ok=True)
    return upload_dir

# 生成唯一文件名
def get_unique_filename(filename: str) -> str:
    """生成唯一的文件名，避免文件名冲突"""
    # 获取文件扩展名
    ext = filename.split('.')[-1] if '.' in filename else ''
    # 生成UUID作为文件名
    unique_name = f"{uuid.uuid4()}.{ext}" if ext else f"{uuid.uuid4()}"
    return unique_name

@router.post("/image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    上传单张图片
    
    - 用户需要登录
    - 支持常见图片格式（jpg, jpeg, png, gif, webp）
    - 返回图片URL供后续使用
    """
    # 校验文件类型
    allowed_extensions = {"jpg", "jpeg", "png", "gif", "webp"}
    file_ext = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
    
    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型，仅支持: {', '.join(allowed_extensions)}"
        )
    
    # 确保上传目录存在
    upload_dir = ensure_upload_dir()
    
    # 生成用户专属目录（按用户ID分组）
    user_dir = upload_dir / f"user_{current_user.id}"
    user_dir.mkdir(exist_ok=True)
    
    # 生成唯一文件名
    unique_filename = get_unique_filename(file.filename)
    file_path = user_dir / unique_filename
    
    try:
        # 保存文件
        with open(file_path, "wb") as buffer:
            buffer.write(await file.read())
        
        # 构建图片URL（相对于服务器根目录）
        relative_path = f"/static/uploads/user_{current_user.id}/{unique_filename}"
        
        # 记录日志
        logger.info(f"用户 {current_user.id} 上传图片: {unique_filename}")
        
        return {
            "success": True,
            "file_name": unique_filename,
            "file_url": relative_path
        }
    except Exception as e:
        logger.error(f"上传图片失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传图片失败: {str(e)}"
        )
