# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# Environment Variables
.env

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# OS
.DS_Store
Thumbs.db

# ------------ WowPicServer 静态资源（按子目录忽略） ------------
# 说明：只忽略 static 下动态或生成的子目录，不排除 static 根目录
static/banners/
static/generated/
static/uploads/
static/styles/

# ------------ 微信支付相关 ------------
1715576316_20250711_cert/