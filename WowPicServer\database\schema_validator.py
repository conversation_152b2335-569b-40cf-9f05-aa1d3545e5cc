from sqlalchemy import inspect
import logging
from typing import Dict, List, Any
from database.models import Base
from config import Config

logger = logging.getLogger(__name__)

class SchemaValidator:
    """数据库结构校验器"""
    
    def __init__(self):
        self.engine = None
        self.inspector = None
    
    def init_validator(self, engine):
        """初始化校验器"""
        self.engine = engine
        self.inspector = inspect(engine)
    
    def get_existing_tables(self) -> List[str]:
        """获取现有表列表"""
        return self.inspector.get_table_names()
    
    def get_table_columns(self, table_name: str) -> Dict[str, Any]:
        """获取表的列信息"""
        columns = {}
        try:
            for column in self.inspector.get_columns(table_name):
                columns[column['name']] = {
                    'type': str(column['type']),
                    'nullable': column['nullable'],
                    'default': column.get('default'),
                    'autoincrement': column.get('autoincrement', False)
                }
        except Exception as e:
            logger.error(f"获取表 {table_name} 列信息失败: {e}")
        return columns
    
    def get_table_indexes(self, table_name: str) -> Dict[str, Any]:
        """获取表的索引信息"""
        indexes = {}
        try:
            for index in self.inspector.get_indexes(table_name):
                indexes[index['name']] = {
                    'columns': index['column_names'],
                    'unique': index['unique']
                }
        except Exception as e:
            logger.error(f"获取表 {table_name} 索引信息失败: {e}")
        return indexes
    
    def get_table_foreign_keys(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表的外键信息"""
        foreign_keys = []
        try:
            for fk in self.inspector.get_foreign_keys(table_name):
                foreign_keys.append({
                    'name': fk['name'],
                    'constrained_columns': fk['constrained_columns'],
                    'referred_table': fk['referred_table'],
                    'referred_columns': fk['referred_columns']
                })
        except Exception as e:
            logger.error(f"获取表 {table_name} 外键信息失败: {e}")
        return foreign_keys
    

    
    def validate_table_structure(self, table_name: str, expected_model) -> Dict[str, List[str]]:
        """校验单个表的结构"""
        issues = {
            'missing_columns': [],       # 模型里有但表中缺失
            'extra_columns': [],         # 表中存在但模型里没有
            'type_mismatches': [],
            'constraint_issues': [],
            'index_issues': [],
            'foreign_key_issues': []
        }
        
        if table_name not in self.get_existing_tables():
            issues['missing_columns'].append(f"表 {table_name} 不存在")
            return issues
        
        # 获取现有列信息
        existing_columns = self.get_table_columns(table_name)
        
        # 获取期望的列信息（从模型中）
        expected_columns = {}
        for column in expected_model.__table__.columns:
            expected_columns[column.name] = {
                'type': str(column.type),
                'nullable': column.nullable,
                'default': column.default,
                'autoincrement': getattr(column, 'autoincrement', False)
            }
        
        # 检查缺失的列
        for col_name, col_info in expected_columns.items():
            if col_name not in existing_columns:
                issues['missing_columns'].append(f"缺失列: {col_name}")
            else:
                # 检查类型匹配（简化检查）
                existing_type = existing_columns[col_name]['type'].upper()
                expected_type = col_info['type'].upper()
                
                # 简化的类型匹配检查
                if not self._types_compatible(existing_type, expected_type):
                    issues['type_mismatches'].append(
                        f"列 {col_name} 类型不匹配: 现有 {existing_type}, 期望 {expected_type}"
                    )
                
                # 检查可空性
                if existing_columns[col_name]['nullable'] != col_info['nullable']:
                    issues['constraint_issues'].append(
                        f"列 {col_name} 可空性不匹配: 现有 {existing_columns[col_name]['nullable']}, 期望 {col_info['nullable']}"
                    )
        
        # 检查多余的列（存在于数据库但不在模型中）
        for col_name in existing_columns.keys():
            if col_name not in expected_columns:
                issues['extra_columns'].append(f"多余列: {col_name}")
        
        # 检查索引
        self._validate_indexes(table_name, expected_model, issues)
        
        # 检查外键
        self._validate_foreign_keys(table_name, expected_model, issues)
        
        return issues
    
    def _types_compatible(self, existing_type: str, expected_type: str) -> bool:
        """检查数据类型是否兼容"""
        # ENUM类型特殊处理
        if 'ENUM' in existing_type and ('VARCHAR' in expected_type or 'ENUM' in expected_type):
            return True
            
        # 简化的类型兼容性检查
        type_mappings = {
            'INTEGER': ['INT', 'INTEGER', 'BIGINT'],
            'VARCHAR': ['VARCHAR', 'TEXT', 'ENUM'],
            'TEXT': ['TEXT', 'LONGTEXT', 'MEDIUMTEXT'],
            'DATETIME': ['DATETIME', 'TIMESTAMP'],
            'BOOLEAN': ['BOOLEAN', 'TINYINT'],
            'JSON': ['JSON', 'TEXT'],
            'ENUM': ['ENUM', 'VARCHAR'],
            'DATE': ['DATE']
        }
        
        for base_type, compatible_types in type_mappings.items():
            if base_type in expected_type:
                return any(comp_type in existing_type for comp_type in compatible_types)
        
        return existing_type == expected_type
    
    def _validate_indexes(self, table_name: str, expected_model, issues: Dict[str, List[str]]):
        """校验索引"""
        existing_indexes = self.get_table_indexes(table_name)
        
        # 检查唯一约束
        for column in expected_model.__table__.columns:
            if column.unique and not column.primary_key:
                # 查找对应的唯一索引
                found_unique = False
                for idx_name, idx_info in existing_indexes.items():
                    if idx_info['unique'] and column.name in idx_info['columns']:
                        found_unique = True
                        break
                
                if not found_unique:
                    issues['index_issues'].append(f"缺失列 {column.name} 的唯一索引")
    
    def _validate_foreign_keys(self, table_name: str, expected_model, issues: Dict[str, List[str]]):
        """校验外键"""
        existing_fks = self.get_table_foreign_keys(table_name)
        
        # 获取期望的外键
        expected_fks = []
        for fk in expected_model.__table__.foreign_keys:
            expected_fks.append({
                'column': fk.parent.name,
                'referred_table': fk.column.table.name,
                'referred_column': fk.column.name
            })
        
        # 检查缺失的外键
        for expected_fk in expected_fks:
            found = False
            for existing_fk in existing_fks:
                if (expected_fk['column'] in existing_fk['constrained_columns'] and
                    expected_fk['referred_table'] == existing_fk['referred_table'] and
                    expected_fk['referred_column'] in existing_fk['referred_columns']):
                    found = True
                    break
            
            if not found:
                issues['foreign_key_issues'].append(
                    f"缺失外键: {expected_fk['column']} -> {expected_fk['referred_table']}.{expected_fk['referred_column']}"
                )
    
    def validate_all_tables(self) -> Dict[str, Dict[str, List[str]]]:
        """校验所有表的结构"""
        all_issues = {}
        
        # 直接从 Base.metadata 获取表信息
        from database.models import User, UserAuthentication, Style, Generation, GenerationImage, SourceImage
        
        models = {
            'users': User,
            'user_authentications': UserAuthentication,
            'styles': Style,
            'generations': Generation,
            'generation_images': GenerationImage,
            'source_images': SourceImage
        }
        
        # 刷新表列表缓存
        self.inspector = inspect(self.engine)
        
        for table_name, model_class in models.items():
            try:
                issues = self.validate_table_structure(table_name, model_class)
                if any(issues.values()):  # 如果有任何问题
                    all_issues[table_name] = issues
            except Exception as e:
                logger.error(f"校验表 {table_name} 时出错: {e}")
                all_issues[table_name] = {'errors': [str(e)]}
        
        return all_issues
    
    def create_missing_tables(self):
        """创建缺失的表"""
        try:
            existing_tables = self.get_existing_tables()
            expected_tables = [table.name for table in Base.metadata.tables.values()]
            
            missing_tables = [table for table in expected_tables if table not in existing_tables]
            
            if missing_tables:
                logger.info(f"✓ 创建数据表: {', '.join(missing_tables)}")
                Base.metadata.create_all(bind=self.engine)
            else:
                logger.info("✓ 所有数据表已存在")
                
        except Exception as e:
            logger.error(f"创建表失败: {e}")
            raise
    
    def auto_fix_schema_issues(self, issues: Dict[str, Dict[str, List[str]]]):
        """自动修复结构问题（目前仅实现新增缺失列）"""
        # 若已显式禁用，直接返回
        if not Config.AUTO_UPDATE_DB_SCHEMA:
            logger.warning("自动更新数据库结构功能已禁用，请手动修复以下问题:")
            self._log_issues(issues)
            return
        
        logger.warning("开始自动修复数据库结构问题...")
        
        from sqlalchemy import text
        # 遍历每张表的问题
        for table_name, table_issues in issues.items():
            # 仅处理缺失列
            missing_cols = []
            for msg in table_issues.get('missing_columns', []):
                if msg.startswith("缺失列:"):
                    col_name = msg.split(":", 1)[1].strip()
                    missing_cols.append(col_name)

            if not missing_cols:
                continue

            # 获取模型类以便读取列定义
            model_class = None
            try:
                for cls in Base._decl_class_registry.values():
                    if hasattr(cls, "__tablename__") and cls.__tablename__ == table_name:
                        model_class = cls
                        break
            except Exception:
                pass

            if model_class is None:
                table_obj = Base.metadata.tables.get(table_name)
                if table_obj is None:
                    logger.error(f"未找到表 {table_name} 的元数据定义，跳过自动修复")
                    continue
                columns_source = table_obj.columns
            else:
                columns_source = model_class.__table__.columns

            # 对每个缺失列生成并执行 ALTER TABLE 语句
            for col in columns_source:
                if col.name not in missing_cols:
                    continue

                # 通过 SQLAlchemy 编译列类型
                col_type_ddl = col.type.compile(dialect=self.engine.dialect)
                nullable_ddl = "NULL" if col.nullable else "NOT NULL"
                default_ddl = ""
                # 处理默认值
                if col.default is not None and col.default.arg is not None:
                    default_val = col.default.arg
                    # 对字符串加引号
                    if isinstance(default_val, str):
                        default_val = f"'{default_val}'"
                    default_ddl = f" DEFAULT {default_val}"

                # 组合语句
                alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col.name} {col_type_ddl} {nullable_ddl}{default_ddl};"
                try:
                    with self.engine.begin() as conn:
                        logger.warning(f"➜ 执行: {alter_sql}")
                        conn.execute(text(alter_sql))
                    logger.info(f"✓ 已为表 {table_name} 新增列 {col.name}")
                except Exception as e:
                    logger.error(f"为表 {table_name} 新增列 {col.name} 失败: {e}")

        logger.warning("自动修复执行完毕，请检查日志确认结果")
    
    def _log_issues(self, issues: Dict[str, Dict[str, List[str]]]):
        """记录结构问题"""
        for table_name, table_issues in issues.items():
            logger.warning(f"表 {table_name} 存在以下问题:")
            for issue_type, issue_list in table_issues.items():
                if issue_list:
                    logger.warning(f"  {issue_type}:")
                    for issue in issue_list:
                        logger.warning(f"    - {issue}")

# 全局校验器实例
schema_validator = SchemaValidator()