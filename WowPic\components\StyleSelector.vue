<template>
  <view class="style-selector-mask" v-if="show" @tap.stop="handleCancel">
    <view class="style-selector-panel" @tap.stop>
      <view class="style-selector-title">
        选择风格
      </view>
      <view class="style-selector-search">
        <view class="search-icon"></view>
        <input class="search-input" placeholder="搜索风格..." v-model="searchText" @input="filterStyles" />
        <view class="clear-icon" v-if="searchText" @click="clearSearch"></view>
      </view>
      <scroll-view class="style-selector-list" scroll-y="true">
        <view class="style-option-item" 
          v-for="style in filteredStyles" 
          :key="style.id" 
          :class="{'active': currentStyleId === style.id}"
          @click="selectStyle(style)">
          <image class="style-option-icon" :src="style.cover_image_url ? getImageUrl(style.cover_image_url) : '/static/头像.png'" mode="aspectFill"></image>
          <view class="style-option-info">
            <text class="style-option-name">{{style.name}}</text>
            <text class="style-option-desc">{{style.description || '精美的图片风格'}}</text>
          </view>
          <view class="style-option-check" v-if="currentStyleId === style.id"></view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '../utils/config.js'
import request from '../utils/request.js'

export default {
  name: 'StyleSelector',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentStyleId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      allStyles: [], // 存储所有风格
      filteredStyles: [], // 筛选后的风格列表
      searchText: '', // 风格搜索文本
      isLoading: false, // 是否正在加载风格列表
    }
  },
  watch: {
    show(newVal) {
      if (newVal && this.allStyles.length === 0) {
        this.fetchAllStyles();
      }
    }
  },
  methods: {
    getImageUrl,
    
    // 获取所有风格
    async fetchAllStyles() {
      try {
        this.isLoading = true;
        const res = await request.get('/wowpic/styles');
        if (Array.isArray(res)) {
          // 先过滤掉通用自由创作风格（model_identifier === 'generic_v1'）
          const filterGeneric = style => style && style.model_identifier !== 'generic_v1';
          const visibleStyles = res.filter(filterGeneric);

          // 按照 is_popular 字段降序排序（数值大的排前面）
          this.allStyles = visibleStyles.sort((a, b) => {
            // 首先比较 is_popular 字段（降序）
            if (b.is_popular !== a.is_popular) {
              return b.is_popular - a.is_popular;
            }
            // 如果 is_popular 相同，则按名称排序（升序）
            return a.name.localeCompare(b.name);
          });
          this.filteredStyles = [...this.allStyles];
        }
      } catch (err) {
        console.error('获取风格列表失败:', err);
        uni.showToast({
          title: '获取风格列表失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
      }
    },
    
    // 筛选风格
    filterStyles() {
      if (!this.searchText) {
        this.filteredStyles = [...this.allStyles];
        return;
      }
      
      const searchText = this.searchText.toLowerCase();
      this.filteredStyles = this.allStyles.filter(style => 
        style.name.toLowerCase().includes(searchText) || 
        (style.description && style.description.toLowerCase().includes(searchText))
      );
    },
    
    // 清空搜索
    clearSearch() {
      this.searchText = '';
      this.filterStyles();
    },
    
    // 选择风格
    selectStyle(style) {
      this.$emit('select', style);
    },
    
    // 取消选择
    handleCancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style>
.style-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
}

.style-selector-panel {
  width: 100%;
  max-height: 70vh;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx 30rpx 30rpx;
  animation: slide-up 0.3s ease;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.style-selector-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
}

.style-selector-title::after {
  content: "";
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #F7A8BE, #D193C8);
  border-radius: 2rpx;
}

.style-selector-search {
  display: flex;
  align-items: center;
  background-color: #F2F6FF;
  border-radius: 20rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23D193C8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.search-input {
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  color: #333;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='15' y1='9' x2='9' y2='15'%3E%3C/line%3E%3Cline x1='9' y1='9' x2='15' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.style-selector-list {
  max-height: 60vh;
  padding: 0 10rpx;
}

.style-option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.style-option-item.active {
  background-color: #F2F6FF;
  border: 2rpx solid #D193C8;
  /* 使用渐变边框效果 */
  position: relative;
}

.style-option-item.active::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16rpx;
  padding: 2rpx;
  background: linear-gradient(90deg, #F7A8BE, #D193C8);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
  pointer-events: none;
}

.style-option-item:active {
  transform: scale(0.98);
  background-color: #F2F6FF;
}

.style-option-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  background-color: #F8F8F8;
}

.style-option-info {
  flex: 1;
}

.style-option-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.style-option-desc {
  font-size: 24rpx;
  color: #999;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.style-option-check {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23D193C8' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 10rpx;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 