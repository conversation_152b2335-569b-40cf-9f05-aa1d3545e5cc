# WowPic 充值系统改进实施方案

## 📋 项目概述

### 目标
- 解决iOS用户无法充值的问题
- 集成微信小程序激励视频广告
- 提供统一的跨平台充值体验
- 增加广告变现收入来源

### 核心功能
- **广告激励**：每次观看获得20哇图币，每日限制6次
- **付费充值**：保留现有安卓付费功能
- **风控机制**：防止用户滥用广告系统
- **统一入口**：单独页面替代弹窗形式

## 🎯 技术方案选择

### 为什么选择单独页面而非弹窗？

**单独页面的优势：**
1. **更好的用户体验**
   - 更大的展示空间，信息层次更清晰
   - 支持更复杂的交互和动画效果
   - 用户操作不会被意外关闭打断

2. **功能扩展性强**
   - 可以添加充值历史、使用统计等功能
   - 支持更多的营销活动和推广内容
   - 便于后续添加会员体系、积分商城等

3. **技术实现优势**
   - 页面生命周期管理更清晰
   - 广告加载和播放状态管理更容易
   - 错误处理和用户引导更完善

4. **商业化考虑**
   - 可以展示更多增值服务
   - 提供更好的品牌展示空间
   - 支持A/B测试和数据分析

## 🏗️ 系统架构设计

### 前端架构
```
pages/
├── recharge/
│   ├── recharge.vue          # 主充值页面
│   ├── components/
│   │   ├── AdRewardCard.vue  # 广告奖励卡片
│   │   ├── PaymentCard.vue   # 付费充值卡片
│   │   └── RechargeHistory.vue # 充值历史
│   └── mixins/
│       └── adManager.js      # 广告管理混入
```

### 后端架构
```
WowPicServer/
├── utils/
│   ├── pay.py               # 现有支付逻辑
│   └── ad_reward.py         # 新增广告奖励逻辑
├── routers/
│   └── ad_reward.py         # 广告奖励路由
└── database/
    └── models.py            # 新增广告观看记录模型
```

## 📱 前端实现方案

### 1. 页面结构设计

**主页面布局：**
```vue
<template>
  <view class="recharge-page">
    <!-- 顶部余额显示 -->
    <view class="balance-header">
      <view class="current-balance">
        <image src="/static/coins.png" class="coin-icon"></image>
        <text class="balance-text">{{ userInfo.coins }}</text>
      </view>
      <text class="balance-label">当前哇图币</text>
    </view>

    <!-- 获取方式选择 -->
    <view class="acquisition-methods">
      <!-- 广告奖励卡片 -->
      <AdRewardCard 
        :daily-count="adWatchCount"
        :max-daily="6"
        @watch-ad="handleWatchAd"
      />
      
      <!-- 付费充值卡片 (仅安卓显示) -->
      <PaymentCard 
        v-if="isAndroid"
        :options="rechargeOptions"
        @select-payment="handlePayment"
      />
    </view>

    <!-- 充值历史 -->
    <RechargeHistory :history="rechargeHistory" />
  </view>
</template>
```

### 2. 广告管理实现

**广告管理混入 (adManager.js)：**
```javascript
export default {
  data() {
    return {
      adUnitId: 'adunit-xxxxxxxxxxxxxxxx', // 微信广告位ID
      rewardedVideoAd: null,
      adLoading: false,
      adWatchCount: 0, // 今日观看次数
    }
  },
  
  onLoad() {
    this.initRewardedVideoAd();
    this.loadAdWatchCount();
  },
  
  methods: {
    // 初始化激励视频广告
    initRewardedVideoAd() {
      if (wx.createRewardedVideoAd) {
        this.rewardedVideoAd = wx.createRewardedVideoAd({
          adUnitId: this.adUnitId
        });
        
        // 监听广告加载成功
        this.rewardedVideoAd.onLoad(() => {
          console.log('激励视频广告加载成功');
          this.adLoading = false;
        });
        
        // 监听广告加载失败
        this.rewardedVideoAd.onError((err) => {
          console.error('激励视频广告加载失败', err);
          this.adLoading = false;
          this.handleAdError(err);
        });
        
        // 监听广告关闭
        this.rewardedVideoAd.onClose((res) => {
          if (res && res.isEnded) {
            // 用户完整观看了广告
            this.handleAdReward();
          } else {
            // 用户中途关闭了广告
            uni.showToast({
              title: '请完整观看广告才能获得奖励',
              icon: 'none'
            });
          }
        });
      }
    },
    
    // 显示激励视频广告
    async showRewardedVideoAd() {
      // 检查每日观看限制
      if (this.adWatchCount >= 6) {
        uni.showToast({
          title: '今日观看次数已达上限',
          icon: 'none'
        });
        return;
      }
      
      this.adLoading = true;
      
      try {
        await this.rewardedVideoAd.show();
      } catch (err) {
        console.error('显示激励视频广告失败', err);
        // 广告未加载完成，先加载再显示
        this.rewardedVideoAd.load()
          .then(() => this.rewardedVideoAd.show())
          .catch(this.handleAdError);
      }
    },
    
    // 处理广告奖励
    async handleAdReward() {
      try {
        uni.showLoading({ title: '发放奖励中...' });
        
        const result = await this.$request.post('/wowpic/ad-reward/claim');
        
        if (result.success) {
          // 更新本地数据
          this.userInfo.coins += 20;
          this.adWatchCount += 1;
          
          // 显示奖励动画
          this.showRewardAnimation();
          
          uni.showToast({
            title: `获得20哇图币！今日还可观看${6 - this.adWatchCount}次`,
            icon: 'success'
          });
        }
      } catch (error) {
        console.error('领取广告奖励失败', error);
        uni.showToast({
          title: '奖励发放失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    }
  }
}
```

### 3. 组件设计

**广告奖励卡片组件：**
```vue
<template>
  <view class="ad-reward-card">
    <view class="card-header">
      <view class="reward-info">
        <text class="reward-title">观看广告免费获取</text>
        <view class="reward-amount">
          <image src="/static/coins.png" class="coin-icon"></image>
          <text class="amount-text">+20</text>
        </view>
      </view>
      <view class="watch-count">
        <text class="count-text">今日已观看 {{ dailyCount }}/{{ maxDaily }}</text>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: (dailyCount / maxDaily * 100) + '%' }"
          ></view>
        </view>
      </view>
    </view>
    
    <view class="card-action">
      <button 
        class="watch-ad-btn"
        :disabled="dailyCount >= maxDaily"
        @click="$emit('watch-ad')"
      >
        {{ dailyCount >= maxDaily ? '今日已达上限' : '立即观看' }}
      </button>
    </view>
  </view>
</template>
```

## 🔧 后端实现方案

### 1. 数据库模型设计

**广告观看记录模型：**
```python
# 在 database/models.py 中添加
class AdWatchRecord(Base):
    __tablename__ = "ad_watch_records"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    watch_date = Column(Date, nullable=False, comment="观看日期")
    watch_count = Column(Integer, default=0, comment="当日观看次数")
    total_rewards = Column(Integer, default=0, comment="当日获得奖励")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 建立与用户的关系
    user = relationship("User", back_populates="ad_watch_records")

# 在 User 模型中添加关系
# user.ad_watch_records = relationship("AdWatchRecord", back_populates="user")
```

### 2. 广告奖励路由实现

**创建 routers/ad_reward.py：**
```python
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import date, datetime
from database.database import get_db
from database.models import User, AdWatchRecord, CoinTransaction, CoinTransactionSource
from utils.auth import get_current_user
import logging

router = APIRouter(prefix="/ad-reward", tags=["广告奖励"])
logger = logging.getLogger(__name__)

# 配置常量
DAILY_AD_LIMIT = 6  # 每日观看上限
AD_REWARD_AMOUNT = 20  # 每次奖励金币数

@router.get("/status")
async def get_ad_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户今日广告观看状态"""
    today = date.today()
    
    # 查询今日观看记录
    record = db.query(AdWatchRecord).filter(
        AdWatchRecord.user_id == current_user.id,
        AdWatchRecord.watch_date == today
    ).first()
    
    watch_count = record.watch_count if record else 0
    
    return {
        "daily_count": watch_count,
        "daily_limit": DAILY_AD_LIMIT,
        "remaining": max(0, DAILY_AD_LIMIT - watch_count),
        "reward_amount": AD_REWARD_AMOUNT
    }

@router.post("/claim")
async def claim_ad_reward(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """领取广告观看奖励"""
    today = date.today()
    
    # 查询或创建今日记录
    record = db.query(AdWatchRecord).filter(
        AdWatchRecord.user_id == current_user.id,
        AdWatchRecord.watch_date == today
    ).first()
    
    if not record:
        record = AdWatchRecord(
            user_id=current_user.id,
            watch_date=today,
            watch_count=0,
            total_rewards=0
        )
        db.add(record)
    
    # 检查是否超过每日限制
    if record.watch_count >= DAILY_AD_LIMIT:
        raise HTTPException(
            status_code=400, 
            detail=f"今日观看次数已达上限({DAILY_AD_LIMIT}次)"
        )
    
    # 更新记录
    record.watch_count += 1
    record.total_rewards += AD_REWARD_AMOUNT
    record.updated_at = datetime.utcnow()
    
    # 增加用户金币
    current_user.coins += AD_REWARD_AMOUNT
    
    # 记录金币流水
    transaction = CoinTransaction(
        user_id=current_user.id,
        change=AD_REWARD_AMOUNT,
        balance=current_user.coins,
        source=CoinTransactionSource.AD_REWARD,
        remark=f"观看广告奖励(第{record.watch_count}次)"
    )
    db.add(transaction)
    
    db.commit()
    
    logger.info(f"用户 {current_user.id} 观看广告获得 {AD_REWARD_AMOUNT} 哇图币，今日第 {record.watch_count} 次")
    
    return {
        "success": True,
        "reward_amount": AD_REWARD_AMOUNT,
        "new_balance": current_user.coins,
        "daily_count": record.watch_count,
        "remaining": DAILY_AD_LIMIT - record.watch_count
    }
```

### 3. 更新金币流水来源

**在 database/models.py 中更新：**
```python
class CoinTransactionSource(str, Enum):
    RECHARGE = "RECHARGE"      # 充值
    GENERATE = "GENERATE"      # 生成图片消费
    MANUAL = "MANUAL"          # 手动调整
    AD_REWARD = "AD_REWARD"    # 广告奖励 (新增)
```

## 🔄 页面路由和导航

### 1. 更新页面配置

**在 pages.json 中添加：**
```json
{
  "pages": [
    {
      "path": "pages/recharge/recharge",
      "style": {
        "navigationBarTitleText": "获取哇图币",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    }
  ]
}
```

### 2. 修改现有充值入口

**更新 profile.vue 中的 openRecharge 方法：**
```javascript
openRecharge() {
  console.log('用户点击充值按钮，跳转到充值页面');
  uni.navigateTo({
    url: '/pages/recharge/recharge'
  });
}
```

## 📊 数据统计和分析

### 1. 用户行为统计

**添加统计接口：**
```python
@router.get("/statistics")
async def get_ad_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户广告观看统计"""
    # 总观看次数
    total_watches = db.query(func.sum(AdWatchRecord.watch_count)).filter(
        AdWatchRecord.user_id == current_user.id
    ).scalar() or 0
    
    # 总获得奖励
    total_rewards = db.query(func.sum(AdWatchRecord.total_rewards)).filter(
        AdWatchRecord.user_id == current_user.id
    ).scalar() or 0
    
    # 最近7天记录
    week_ago = date.today() - timedelta(days=7)
    recent_records = db.query(AdWatchRecord).filter(
        AdWatchRecord.user_id == current_user.id,
        AdWatchRecord.watch_date >= week_ago
    ).all()
    
    return {
        "total_watches": total_watches,
        "total_rewards": total_rewards,
        "recent_activity": [
            {
                "date": record.watch_date.isoformat(),
                "count": record.watch_count,
                "rewards": record.total_rewards
            }
            for record in recent_records
        ]
    }
```

## 🚀 实施计划

### 第一阶段：基础功能开发 (1周)
1. 创建充值页面和基础组件
2. 集成微信激励视频广告API
3. 实现后端广告奖励接口
4. 数据库模型创建和迁移

### 第二阶段：功能完善 (1周)
1. 添加风控和限制机制
2. 完善错误处理和用户提示
3. 实现充值历史和统计功能
4. 添加动画效果和用户体验优化

### 第三阶段：测试和优化 (3-5天)
1. 功能测试和bug修复
2. 性能优化和代码审查
3. 用户体验测试和调整
4. 上线准备和文档完善

## 📋 待补充内容

请您提供微信小程序激励视频广告的具体开发文档，我将据此完善：
1. 广告SDK的具体集成方法
2. 广告位申请和配置流程
3. 广告回调处理的详细实现
4. 错误码处理和异常情况应对

这样我们就能确保实现方案完全符合微信官方的技术要求。
