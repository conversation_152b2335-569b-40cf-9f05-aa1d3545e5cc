#!/usr/bin/env python3
"""
测试ratio参数重构的正确性
验证统一的提示词构建函数是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.generation_router import build_final_ai_prompt
from database.models import TemplateType
from typing import Dict

class MockStyle:
    """模拟风格对象"""
    def __init__(self, template_type: TemplateType, prompt_template: str = ""):
        self.template_type = template_type
        self.prompt_template = prompt_template

def test_build_final_ai_prompt():
    """测试统一的提示词构建函数"""
    print("🧪 开始测试 build_final_ai_prompt 函数...")
    
    # 测试1: FULL_PROMPT类型，默认ratio
    print("\n📝 测试1: FULL_PROMPT类型，默认ratio")
    style = MockStyle(TemplateType.FULL_PROMPT, "一只可爱的猫咪")
    result = build_final_ai_prompt(style, "一只橘猫在睡觉", {}, "1:1")
    expected = "一只橘猫在睡觉\n\"ratio\": \"1:1\""
    print(f"输入: prompt='一只橘猫在睡觉', ratio='1:1'")
    print(f"输出: {result}")
    print(f"预期: {expected}")
    assert result == expected, f"测试1失败: 期望 '{expected}', 实际 '{result}'"
    print("✅ 测试1通过")
    
    # 测试2: FULL_PROMPT类型，非默认ratio
    print("\n📝 测试2: FULL_PROMPT类型，非默认ratio")
    style = MockStyle(TemplateType.FULL_PROMPT, "一只可爱的猫咪")
    result = build_final_ai_prompt(style, "一只橘猫在睡觉", {}, "3:4")
    expected = "一只橘猫在睡觉\n\"ratio\": \"3:4\""
    print(f"输入: prompt='一只橘猫在睡觉', ratio='3:4'")
    print(f"输出: {result}")
    print(f"预期: {expected}")
    assert result == expected, f"测试2失败: 期望 '{expected}', 实际 '{result}'"
    print("✅ 测试2通过")
    
    # 测试3: FULL_PROMPT类型，使用模板
    print("\n📝 测试3: FULL_PROMPT类型，使用模板")
    style = MockStyle(TemplateType.FULL_PROMPT, "一只可爱的猫咪")
    result = build_final_ai_prompt(style, None, {}, "4:3")
    expected = "一只可爱的猫咪\n\"ratio\": \"4:3\""
    print(f"输入: prompt=None, ratio='4:3'")
    print(f"输出: {result}")
    print(f"预期: {expected}")
    assert result == expected, f"测试3失败: 期望 '{expected}', 实际 '{result}'"
    print("✅ 测试3通过")
    
    print("\n🎉 所有测试通过！ratio参数重构正确工作。")

def test_prompt_format():
    """测试提示词格式的一致性"""
    print("\n🧪 开始测试提示词格式一致性...")

    # 测试项目实际支持的ratio值 - 现在所有ratio都会被添加
    test_cases = [
        ("1:1", "一只猫", "一只猫\n\"ratio\": \"1:1\""),  # 默认ratio也要添加
        ("3:4", "一只猫", "一只猫\n\"ratio\": \"3:4\""),  # 竖屏
        ("4:3", "一只猫", "一只猫\n\"ratio\": \"4:3\""),  # 横屏
    ]

    style = MockStyle(TemplateType.FULL_PROMPT)

    for ratio, prompt, expected in test_cases:
        result = build_final_ai_prompt(style, prompt, {}, ratio)
        print(f"ratio='{ratio}' -> '{result}'")
        assert result == expected, f"格式测试失败: ratio={ratio}, 期望 '{expected}', 实际 '{result}'"

    print("✅ 提示词格式测试通过")

def test_variable_prompt_with_ratio():
    """测试VARIABLE_PROMPT类型的ratio处理"""
    print("\n🧪 开始测试VARIABLE_PROMPT类型的ratio处理...")

    # 模拟宠物证件照风格
    style = MockStyle(TemplateType.VARIABLE_PROMPT, "参考这张图片，帮我生成一张证件照，{background_style}，一寸大小")

    # 测试用例 - 现在所有ratio都会被添加
    test_cases = [
        # (variables, ratio, expected_result)
        ({"background_style": "白底"}, "1:1", "参考这张图片，帮我生成一张证件照，白底，一寸大小\n\"ratio\": \"1:1\""),
        ({"background_style": "蓝底"}, "3:4", "参考这张图片，帮我生成一张证件照，蓝底，一寸大小\n\"ratio\": \"3:4\""),
        ({"background_style": "红底"}, "4:3", "参考这张图片，帮我生成一张证件照，红底，一寸大小\n\"ratio\": \"4:3\""),
    ]

    for variables, ratio, expected in test_cases:
        result = build_final_ai_prompt(style, None, variables, ratio)
        print(f"variables={variables}, ratio='{ratio}'")
        print(f"结果: {result}")
        print(f"预期: {expected}")
        assert result == expected, f"VARIABLE_PROMPT测试失败: 期望 '{expected}', 实际 '{result}'"
        print("✅ 通过")
        print()

    print("✅ VARIABLE_PROMPT类型ratio处理测试通过")

def main():
    """主测试函数"""
    print("🚀 开始ratio参数重构测试")
    print("=" * 50)
    
    try:
        test_build_final_ai_prompt()
        test_prompt_format()
        test_variable_prompt_with_ratio()

        print("\n" + "=" * 50)
        print("🎊 所有测试通过！重构成功！")
        print("\n📋 重构总结:")
        print("✅ 统一的提示词构建函数正常工作")
        print("✅ ratio参数格式化正确")
        print("✅ 默认ratio值处理正确")
        print("✅ VARIABLE_PROMPT类型ratio处理正确")
        print("✅ 数据流转规则统一")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
