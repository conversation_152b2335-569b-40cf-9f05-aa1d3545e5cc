/* 图标样式统一管理文件 */

/* 设置图标 */
.icon-setting {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' x='0px' y='0px' width='48' height='48' viewBox='0,0,255.99825,255.99825'><g fill='none' fill-rule='nonzero' stroke='none' stroke-width='none' stroke-linecap='none' stroke-linejoin='none' stroke-miterlimit='10' stroke-dasharray='' stroke-dashoffset='0' font-family='none' font-weight='none' font-size='none' text-anchor='none' style='mix-blend-mode: normal'><g transform='scale(5.33333,5.33333)'><path d='M34.8,28.7c-0.6,-1.7 -1.5,-3.2 -2.6,-4.6c-0.3,-0.3 -0.7,-0.4 -1.1,-0.3l-0.7,0.3c-0.6,0.2 -1.2,0.3 -1.9,0.2c-1.6,-0.2 -2.7,-1.4 -2.9,-2.9l-0.1,-0.9c-0.1,-0.4 -0.4,-0.7 -0.8,-0.8c-0.9,-0.2 -1.7,-0.3 -2.6,-0.3c-0.9,0 -1.8,0.1 -2.6,0.3c-0.4,0.1 -0.7,0.4 -0.8,0.8l-0.1,0.8c-0.2,1.5 -1.4,2.7 -2.9,2.9c-0.6,0.1 -1.3,0 -1.9,-0.2l-0.8,-0.2c-0.4,-0.1 -0.8,0 -1.1,0.3c-1.2,1.3 -2.1,2.9 -2.6,4.6c-0.1,0.4 0,0.8 0.3,1.1l0.7,0.5c0.7,0.7 1.2,1.7 1.2,2.7c0,1 -0.5,2 -1.3,2.7l-0.7,0.5c-0.3,0.3 -0.5,0.7 -0.3,1.1c0.6,1.7 1.5,3.2 2.6,4.6c0.3,0.3 0.7,0.4 1.1,0.3l0.7,-0.3c0.6,-0.2 1.2,-0.3 1.9,-0.2c1.6,0.2 2.7,1.4 2.9,2.9l0.1,0.9c0.1,0.4 0.4,0.7 0.8,0.8c0.9,0.2 1.7,0.3 2.6,0.3c0.9,0 1.8,-0.1 2.6,-0.3c0.4,-0.1 0.7,-0.4 0.8,-0.8l0.1,-0.8c0.2,-1.5 1.4,-2.7 2.9,-2.9c0.6,-0.1 1.3,0 1.9,0.2l0.7,0.3c0.4,0.1 0.8,0 1.1,-0.3c1.2,-1.3 2.1,-2.9 2.6,-4.6c0.1,-0.4 0,-0.8 -0.3,-1.1l-0.7,-0.5c-0.6,-0.8 -1.1,-1.8 -1.1,-2.8c0,-1 0.5,-2 1.3,-2.7l0.7,-0.5c0.3,-0.3 0.4,-0.7 0.3,-1.1z' fill='%237562ff' stroke='none' stroke-width='1' stroke-linecap='butt' stroke-linejoin='miter'></path><path d='M47.9,14.9c-0.4,-1.2 -1,-2.2 -1.8,-3.2c-0.2,-0.2 -0.5,-0.3 -0.8,-0.2l-0.5,0.2c-0.4,0.2 -0.9,0.2 -1.3,0.2c-1.1,-0.2 -1.9,-1 -2,-2l-0.1,-0.6c0,-0.3 -0.3,-0.5 -0.5,-0.6c-0.6,-0.1 -1.2,-0.2 -1.8,-0.2c-0.6,0 -1.2,0.1 -1.8,0.2c-0.3,0.1 -0.5,0.3 -0.5,0.6l-0.1,0.6c-0.2,1 -1,1.8 -2,2c-0.4,0.1 -0.9,0 -1.3,-0.2l-0.5,-0.2c-0.3,-0.1 -0.6,0 -0.8,0.2c-0.8,0.9 -1.4,2 -1.8,3.2c-0.1,0.3 0,0.6 0.2,0.8l0.5,0.4c0.6,0.5 0.9,1.2 0.9,1.9c0,0.7 -0.3,1.4 -0.9,1.9l-0.5,0.4c-0.2,0.2 -0.3,0.5 -0.2,0.8c0.4,1.2 1,2.2 1.8,3.2c0.2,0.2 0.5,0.3 0.8,0.2l0.5,-0.2c0.4,-0.2 0.9,-0.2 1.3,-0.2c1.1,0.2 1.9,1 2,2l0.1,0.6c0,0.3 0.3,0.5 0.5,0.6c0.6,0.1 1.2,0.2 1.8,0.2c0.6,0 1.2,-0.1 1.8,-0.2c0.3,-0.1 0.5,-0.3 0.5,-0.6l0.1,-0.6c0.2,-1 1,-1.8 2,-2c0.4,-0.1 0.9,0 1.3,0.2l0.5,0.2c0.3,0.1 0.6,0 0.8,-0.2c0.8,-0.9 1.4,-2 1.8,-3.2c0.1,-0.3 0,-0.6 -0.2,-0.8l-0.5,-0.4c-0.6,-0.5 -0.9,-1.2 -0.9,-1.9c0,-0.7 0.3,-1.4 0.9,-1.9l0.5,-0.4c0.2,-0.3 0.3,-0.6 0.2,-0.8z' fill='%237562ff' stroke='none' stroke-width='1' stroke-linecap='butt' stroke-linejoin='miter'></path><circle cx='35' cy='14' r='2.5' fill='%2318193f' stroke='none' stroke-width='1' stroke-linecap='butt' stroke-linejoin='miter'></circle><path d='M27.7,38.4c1.5,-1.6 2.7,-3.5 3.3,-5.7l-1.2,-1c-0.8,-0.7 -1.3,-1.7 -1.3,-2.7c0,-1 0.5,-2 1.3,-2.7l1.2,-1c-0.6,-2.2 -1.8,-4.1 -3.3,-5.7l-1.4,0.5c-1,0.4 -2.1,0.3 -3,-0.2c-0.9,-0.5 -1.5,-1.4 -1.7,-2.5l-0.3,-1.5c-1.1,-0.3 -2.2,-0.4 -3.3,-0.4c-1.1,0 -2.2,0.2 -3.3,0.4' fill='none' stroke='%2318193f' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'></path><path d='M12.8,19.9c-0.9,0.5 -2,0.6 -3,0.2l-1.4,-0.5c-1.5,1.6 -2.7,3.5 -3.3,5.7l1.2,1c0.7,0.7 1.2,1.7 1.2,2.7c0,1 -0.5,2 -1.3,2.7l-1.2,1c0.6,2.2 1.8,4.1 3.3,5.7l1.4,-0.5c1,-0.4 2.1,-0.3 3,0.2c0.9,0.5 1.5,1.4 1.7,2.5l0.2,1.5c1.1,0.3 2.2,0.4 3.3,0.4c1.1,0 2.2,-0.2 3.3,-0.4l0.2,-1.5c0.2,-1 0.8,-1.9 1.7,-2.5' fill='none' stroke='%2318193f' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'></path><circle cx='18' cy='29' r='4.5' fill='none' stroke='%2318193f' stroke-width='3' stroke-linecap='butt' stroke-linejoin='miter'></circle><path d='M35,23.2c0.8,0 1.6,-0.1 2.3,-0.3l0.2,-1.1c0.1,-0.7 0.6,-1.3 1.2,-1.7c0.6,-0.4 1.4,-0.4 2.1,-0.2l1,0.4c1.1,-1.1 1.9,-2.4 2.3,-4l-0.8,-0.7c-0.6,-0.5 -0.9,-1.2 -0.9,-1.9c0,-0.7 0.3,-1.4 0.9,-1.9l0.8,-0.7c-0.4,-1.5 -1.2,-2.9 -2.3,-4l-1,0.4c-0.8,0.5 -1.5,0.4 -2.2,0.1c-0.6,-0.4 -1.1,-1 -1.2,-1.7l-0.2,-1.1c-0.7,-0.2 -1.5,-0.3 -2.3,-0.3c-0.8,0 -1.6,0.1 -2.3,0.3l-0.2,1.1c-0.1,0.7 -0.6,1.3 -1.2,1.7c-0.5,0.3 -1.2,0.4 -1.9,0.1l-1,-0.4c-1.1,1.1 -1.9,2.4 -2.3,4l0.8,0.7c0.6,0.5 0.9,1.2 0.9,1.9' fill='none' stroke='%2318193f' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'></path></g></g></svg>");
  background-size: 100%;
}

/* 签到图标 */
.icon-sign {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3Cpath d='M8 14h.01'%3E%3C/path%3E%3Cpath d='M12 14h.01'%3E%3C/path%3E%3Cpath d='M16 14h.01'%3E%3C/path%3E%3Cpath d='M8 18h.01'%3E%3C/path%3E%3Cpath d='M12 18h.01'%3E%3C/path%3E%3Cpath d='M16 18h.01'%3E%3C/path%3E%3C/svg%3E");
}

/* 邀请图标 */
.icon-invite {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12 2l-8 4.5v9L12 21l8-4.5v-9L12 2z'%3E%3C/path%3E%3Cpath d='M12 9v12'%3E%3C/path%3E%3Cpath d='M12 4l8 5'%3E%3C/path%3E%3Cpath d='M12 4l-8 5'%3E%3C/path%3E%3Cpath d='M8.1 9.5L12 7l3.9 2.5'%3E%3C/path%3E%3C/svg%3E");
}

/* 客服图标 */
.icon-service {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%237562FF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z'%3E%3C/path%3E%3Cpath d='M8 10h.01'%3E%3C/path%3E%3Cpath d='M12 10h.01'%3E%3C/path%3E%3Cpath d='M16 10h.01'%3E%3C/path%3E%3C/svg%3E");
}

/* 返回图标 */
.back-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M19 12H5'%3E%3C/path%3E%3Cpath d='M12 19l-7-7 7-7'%3E%3C/path%3E%3C/svg%3E");
}

/* 上传图标 */
.upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234A90E2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
}

/* 保存图标 */
.save-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='7 10 12 15 17 10'%3E%3C/polyline%3E%3Cline x1='12' y1='15' x2='12' y2='3'%3E%3C/line%3E%3C/svg%3E");
}

/* 分享图标 */
.share-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23FFFFFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'%3E%3C/circle%3E%3Ccircle cx='6' cy='12' r='3'%3E%3C/circle%3E%3Ccircle cx='18' cy='19' r='3'%3E%3C/circle%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'%3E%3C/line%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'%3E%3C/line%3E%3C/svg%3E");
}

/* 图标通用样式 */
.tool-icon, .action-icon {
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
} 