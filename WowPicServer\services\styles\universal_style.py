import logging
from typing import Dict, List
from sqlalchemy.orm import Session

from .base_style import BaseStyle
from services.providers.base_provider import BaseProvider
from database.models import Style
from .style_config import get_style_config

logger = logging.getLogger(__name__)

class UniversalStyle(BaseStyle):
    """通用风格实现类，使用指定的API厂商处理图片生成"""

    def __init__(self, provider: BaseProvider, model_identifier: str, db_style: Style = None):
        """
        初始化通用风格实现
        
        Args:
            provider: API厂商实现实例
            model_identifier: 风格标识符
            db_style: 数据库中的风格配置（可选）
        """
        self.provider = provider
        self.model_identifier = model_identifier
        self.db_style = db_style
        
        logger.info(f"初始化UniversalStyle: {model_identifier} -> {provider.provider_name}")

    async def generate(self, params: Dict) -> List[str]:
        """生成图片核心方法，返回一个或多个图片URL列表"""
        try:
            # 验证参数
            self.validate_params(params)
            
            # 提取参数
            prompt = params.get("prompt", "")
            source_image_urls = params.get("source_image_urls", [])

            # 兼容单图模式
            source_image_url = params.get("source_image_url")
            if source_image_url and source_image_url not in source_image_urls:
                source_image_urls = [source_image_url] + source_image_urls

            logger.info(f"UniversalStyle[{self.model_identifier}] 开始生成图片")
            logger.info(f"提示词长度: {len(prompt)}, 源图片数量: {len(source_image_urls)}")

            # 简化：不传递ratio和n参数，让厂商直接处理完整提示词
            # 支持n参数的模型默认生成2张图片
            # 不支持n参数的模型固定生成1张图片
            image_urls = await self.provider.generate_image(prompt, source_image_urls)
            
            logger.info(f"UniversalStyle[{self.model_identifier}] 生成成功，返回 {len(image_urls)} 张图片")
            return image_urls
            
        except Exception as e:
            logger.error(f"UniversalStyle[{self.model_identifier}] 生成失败: {e}")
            raise

    def validate_params(self, params: Dict) -> bool:
        """验证输入参数"""
        if params is None:
            raise ValueError("参数不能为空")
        
        # 使用厂商的参数验证
        return self.provider.validate_params(params)

    @property
    def name(self) -> str:
        """风格名称标识"""
        return self.model_identifier
    
    def get_style_config(self) -> Dict:
        """获取风格配置信息"""
        config = {
            'model_identifier': self.model_identifier,
            'provider': self.provider.provider_name,
            'supported_features': self.provider.supported_features
        }
        
        if self.db_style:
            config.update({
                'name': self.db_style.name,
                'description': self.db_style.description,
                'template_type': self.db_style.template_type.value if self.db_style.template_type else None,
                'cost': self.db_style.cost,
                'settings': self.db_style.settings or {}
            })
        
        return config
    
    def get_prefill_clause(self, image_count: int) -> str:
        """
        根据上传的图片数量，返回预设的动态提示词部分
        只有特定风格才有预填充逻辑
        """
        # 猫咪去旅行风格的预填充
        if self.model_identifier == "cat_travel_v1":
            return self._get_cat_travel_prefill(image_count)
        elif self.model_identifier == "cat_travel_v2":
            return self._get_cat_travel_prefill(image_count)
        # 名人合照风格的预填充
        elif self.model_identifier == "celebrity_selfie_v1":
            return self._get_celebrity_selfie_prefill(image_count)
        else:
            # 其他风格不使用预填充
            # 注意：宠物证件照风格使用选项选择，不需要预填充
            return ""
    
    def _get_cat_travel_prefill(self, image_count: int) -> str:
        """猫咪去旅行风格的预填充逻辑"""
        if image_count == 0:
            return "主角是图一，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 1:
            return "主角是图一，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 2:
            return "主角是图一和图二，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 3:
            return "主角是图一和图二，晚上，旁边是图三"
        elif image_count == 4:
            return "主角是图一、图二、图三，晚上，旁边是图四"
        
        # 默认或超出预设数量的规则
        subjects = "、".join([f"图{i+1}" for i in range(image_count)])
        return f"主角是{subjects}，晚上，旁边是上海东方明珠，在黄浦江旁边"

    def _get_celebrity_selfie_prefill(self, image_count: int) -> str:
        """名人合照风格的预填充逻辑"""
        if image_count == 0:
            return "主角是图一和刘亦菲两个人，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 1:
            return "主角是图一和刘亦菲两个人，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 2:
            return "主角是图一、图二和刘亦菲三个人，晚上，旁边是上海东方明珠，在黄浦江旁边"
        elif image_count == 3:
            return "主角是图一和刘亦菲，晚上，旁边是图三作为背景"
        elif image_count == 4:
            return "主角是图一、图二和刘亦菲，晚上，旁边是图四作为背景"

        # 超出预设数量的情况
        subjects = "、".join([f"图{i+1}" for i in range(image_count)])
        return f"主角是{subjects}，与偶像一起自拍"
