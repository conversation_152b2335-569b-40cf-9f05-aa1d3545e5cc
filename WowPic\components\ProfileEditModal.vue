<template>
	<view class="profile-edit-modal" v-if="modelValue">
		<view class="modal-mask" @click="closeModal"></view>
		<view class="modal-content">
			<view class="modal-header">
				<text class="modal-title">编辑个人资料</text>
				<view class="modal-close" @click="closeModal">×</view>
			</view>
			
			<view class="modal-body">
				<!-- 头像上传 -->
				<view class="avatar-section">
					<text class="section-title">更换头像</text>
					<button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<image class="avatar-preview" :src="tempAvatarUrl ? tempAvatarUrl : userInfo.avatar_url ? getImageUrl(userInfo.avatar_url) : '/static/头像.png'"></image>
						<view class="avatar-upload-icon">
							<view class="icon-camera"></view>
						</view>
					</button>
					<text class="avatar-tip">点击头像更换</text>
				</view>
				
				<!-- 昵称编辑 -->
				<view class="nickname-section">
					<text class="section-title">修改昵称</text>
					<input type="nickname" class="nickname-input" placeholder="请输入昵称" v-model="nickname" maxlength="12" @blur="onNicknameBlur" />
					<text class="nickname-tip">1-12个字符</text>
				</view>
			</view>
			
			<view class="modal-footer">
				<view class="btn-cancel" @click="closeModal">取消</view>
				<view class="btn-save" @click="saveProfile" :class="{ 'btn-disabled': !hasChanges || isSaving }">
					<view class="loading-spinner" v-if="isSaving"></view>
					<text>{{ isSaving ? '保存中...' : '保存' }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../utils/request.js';
	import { getImageUrl, baseUrl } from '../utils/config.js';
	
	export default {
		name: "ProfileEditModal",
		props: {
			modelValue: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				nickname: '',
				tempAvatarUrl: '',
				tempAvatarFile: null,
				originalNickname: '',
				isSaving: false,
				getImageUrl
			}
		},
		computed: {
			hasChanges() {
				return this.nickname !== this.originalNickname || this.tempAvatarFile !== null;
			}
		},
		watch: {
			modelValue(val) {
				if (val) {
					// 当弹窗打开时，初始化数据
					this.initData();
				}
			}
		},
		methods: {
			initData() {
				this.nickname = this.userInfo.nickname || '';
				this.originalNickname = this.userInfo.nickname || '';
				this.tempAvatarUrl = '';
				this.tempAvatarFile = null;
			},
			closeModal() {
				this.$emit('update:modelValue', false);
			},
			onChooseAvatar(e) {
				const { avatarUrl } = e.detail;
				this.tempAvatarUrl = avatarUrl;
				this.tempAvatarFile = avatarUrl; // 保存文件路径，用于后续上传
			},
			onNicknameBlur(e) {
				// 微信会在blur事件中进行安全检测，这里可以添加额外的验证逻辑
				this.nickname = this.nickname.trim();
			},
			async saveProfile() {
				if (!this.hasChanges || this.isSaving) return;
				
				this.isSaving = true;
				
				try {
					let avatarPath = null;
					
					// 如果有头像更新，先上传头像
					if (this.tempAvatarFile) {
						try {
							const uploadResult = await this.uploadAvatar(this.tempAvatarFile);
							avatarPath = uploadResult.file_path;
							console.log('头像上传成功，路径:', avatarPath);
						} catch (error) {
							console.error('头像上传失败:', error);
							uni.showToast({
								title: '头像上传失败',
								icon: 'none'
							});
							this.isSaving = false;
							return;
						}
					}
					
					// 构建更新数据
					const updateData = {};
					if (this.nickname !== this.originalNickname) {
						updateData.nickname = this.nickname;
					}
					if (avatarPath) {
						updateData.avatar_url = avatarPath;
					}
					
					// 调用更新接口
					// 使用post方法代替put方法，因为put可能未正确导出
					const res = await request.post('/wowpic/auth/profile', updateData);
					
					// 更新成功
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					
					// 通知父组件更新成功，保存原始路径
					this.$emit('update-success', {
						nickname: this.nickname,
						avatar_url: avatarPath || this.userInfo.avatar_url
					});
					
					// 关闭弹窗
					this.closeModal();
				} catch (error) {
					console.error('保存个人资料失败:', error);
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				} finally {
					this.isSaving = false;
				}
			},
			async uploadAvatar(filePath) {
				// 使用完整URL而不是request.baseUrl
				const apiUrl = baseUrl + '/wowpic/auth/avatar';
				console.log('上传头像URL:', apiUrl);
				
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: apiUrl, // 使用完整URL
						filePath: filePath,
						name: 'avatar',
						header: {
							'Authorization': `Bearer ${uni.getStorageSync('token')}`
						},
						success: (res) => {
							if (res.statusCode === 200) {
								try {
									const data = JSON.parse(res.data);
									resolve(data);
								} catch (error) {
									console.error('解析响应数据失败:', error, res.data);
									reject(new Error('解析响应数据失败'));
								}
							} else {
								console.error('上传失败，状态码:', res.statusCode, res.data);
								reject(new Error(`上传失败，状态码: ${res.statusCode}`));
							}
						},
						fail: (err) => {
							console.error('上传请求失败:', err);
							reject(err);
						}
					});
				});
			}
		}
	}
</script>

<style>
	.profile-edit-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
	}
	
	.modal-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
	}
	
	.modal-content {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		border-radius: 24rpx 24rpx 0 0;
		padding: 30rpx;
		animation: slideUp 0.3s ease;
		max-height: 90vh;
		overflow-y: auto;
	}
	
	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}
	
	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.modal-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.modal-close {
		width: 44rpx;
		height: 44rpx;
		line-height: 40rpx;
		text-align: center;
		font-size: 36rpx;
		color: #999999;
		background-color: #F0F0F0;
		border-radius: 50%;
	}
	
	.modal-body {
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.avatar-section {
		margin-bottom: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.avatar-wrapper {
		width: 160rpx;
		height: 160rpx;
		position: relative;
		margin: 0;
		padding: 0;
		background: none;
		border: none;
		line-height: normal;
	}
	
	.avatar-wrapper::after {
		border: none;
	}
	
	.avatar-preview {
		width: 160rpx;
		height: 160rpx;
		border-radius: 80rpx;
		border: 4rpx solid #EEEEEE;
	}
	
	.avatar-upload-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 50rpx;
		height: 50rpx;
		background-color: #4F7BD1; /* 更新为与logo相符的蓝色 */
		border-radius: 25rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		border: 2rpx solid rgba(255, 255, 255, 0.6);
	}
	
	.icon-camera {
		width: 30rpx;
		height: 30rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M9 3L7.17 5H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2h-3.17L15 3H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-9c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}
	
	.avatar-tip {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
	}
	
	.nickname-section {
		margin-bottom: 30rpx;
	}
	
	.nickname-input {
		width: 100%;
		height: 80rpx;
		background-color: #F8F8F8;
		border-radius: 12rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}
	
	.nickname-tip {
		font-size: 24rpx;
		color: #999999;
	}
	
	.modal-footer {
		display: flex;
		justify-content: flex-end;
	}
	
	.btn-cancel {
		height: 80rpx;
		padding: 0 30rpx;
		background-color: #F0F0F0;
		border-radius: 40rpx;
		font-size: 28rpx;
		color: #666666;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.btn-save {
		height: 80rpx;
		padding: 0 30rpx;
		background: linear-gradient(135deg, #6291E7 0%, #4F7BD1 100%); /* 更新为与logo相符的蓝色渐变 */
		border-radius: 40rpx;
		font-size: 28rpx;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2rpx solid rgba(255, 255, 255, 0.5);
		box-shadow: 0 4rpx 8rpx rgba(98, 145, 231, 0.3);
	}
	
	.btn-disabled {
		opacity: 0.5;
	}
	
	.loading-spinner {
		width: 30rpx;
		height: 30rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
		border-top: 3rpx solid #FFFFFF;
		border-radius: 50%;
		margin-right: 10rpx;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style> 