import logging
import time
import hashlib
import httpx
import json
from typing import Dict, Optional, List, Any
from fastapi import APIRouter, Request, HTTPException, Depends
from sqlalchemy.orm import Session

from config import Config
from utils.auth import get_db
from database.models import User

# 设置日志记录器
logger = logging.getLogger(__name__)

# FastAPI路由器，用于接收微信的异步回调
router = APIRouter(
    prefix="/wowpic/security",
    tags=["内容安全"],
)

# 缓存已检测的图片结果，避免重复检测
# 格式: {图片URL: {"result": 结果, "timestamp": 检测时间}}
# 结果: 0=安全，1=风险，None=未知（尚未收到结果）
_image_check_results = {}

# 记录待处理的检测任务
# 格式: {trace_id: {"image_url": 图片URL, "timestamp": 提交时间}}
_pending_checks = {}

# 启动时自动创建定时任务，每小时清理一次过期数据
@router.on_event("startup")
async def setup_periodic_cleanup():
    """设置定期清理任务"""
    if not Config.WX_CONTENT_SECURITY_ENABLED:
        logger.info("内容安全检测功能已禁用，跳过设置定期清理任务")
        return
        
    import asyncio
    
    async def periodic_cleanup():
        while True:
            try:
                cleanup_expired_data()
                # 每小时清理一次
                await asyncio.sleep(3600)
            except Exception as e:
                logger.error(f"执行定期清理任务异常: {e}")
                await asyncio.sleep(60)  # 发生异常时，1分钟后重试
    
    # 创建后台任务
    asyncio.create_task(periodic_cleanup())

async def get_access_token() -> Optional[str]:
    """获取微信接口调用凭证access_token"""
    try:
        # 微信获取access_token的接口
        url = f"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={Config.WX_APPID}&secret={Config.WX_SECRET}"
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, timeout=10.0)
            data = response.json()
            
            if "access_token" in data:
                logger.debug(f"成功获取微信access_token")
                return data["access_token"]
            else:
                logger.error(f"❌ 获取access_token失败: {data}")
                return None
    except Exception as e:
        logger.error(f"❌ 获取access_token异常: {e}")
        return None

async def check_image_security(image_url: str) -> Dict[str, Any]:
    """
    提交图片进行内容安全检测
    
    Args:
        image_url: 图片URL，必须确保微信服务器能访问
        
    Returns:
        Dict包含:
        - success: 请求是否成功
        - trace_id: 如果成功，返回检测任务ID
        - message: 额外信息
    """
    # 如果内容安全检测功能已禁用，直接返回成功
    if not Config.WX_CONTENT_SECURITY_ENABLED:
        logger.info(f"内容安全检测功能已禁用，跳过检测: {image_url}")
        return {
            "success": True,
            "from_cache": False,
            "result": 0,  # 默认安全
            "message": "内容安全检测功能已禁用"
        }
        
    # 检查缓存是否已有结果
    if image_url in _image_check_results:
        cache_data = _image_check_results[image_url]
        # 缓存有效期24小时
        if time.time() - cache_data["timestamp"] < 86400:
            logger.debug(f"使用缓存的安全检测结果: {image_url}")
            return {
                "success": True,
                "from_cache": True,
                "result": cache_data["result"],
                "message": "来自缓存的结果"
            }
    
    try:
        # 获取access_token
        access_token = await get_access_token()
        if not access_token:
            return {"success": False, "message": "获取access_token失败"}
        
        # 准备请求参数
        url = f"https://api.weixin.qq.com/wxa/media_check_async?access_token={access_token}"
        
        # 确保URL能被微信服务器访问
        public_image_url = ensure_public_url(image_url)
        
        payload = {
            "media_url": public_image_url,
            "media_type": 2  # 2表示图片
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload, timeout=15.0)
            data = response.json()

            if data.get("errcode") == 0:
                trace_id = data.get("trace_id")
                logger.debug(f"安全检测请求已接受，trace_id: {trace_id}")
                
                # 记录待处理的检测任务
                _pending_checks[trace_id] = {
                    "image_url": image_url,
                    "timestamp": time.time()
                }
                
                # 将图片标记为待检测状态（结果未知）
                _image_check_results[image_url] = {
                    "result": None,  # None表示结果未知
                    "timestamp": time.time()
                }
                
                return {
                    "success": True, 
                    "trace_id": trace_id,
                    "from_cache": False,
                    "result": None,  # 暂无结果
                    "message": "检测请求已提交"
                }
            else:
                logger.error(f"❌ 提交内容安全检测失败: {data}")
                return {"success": False, "message": f"检测请求失败: {data.get('errmsg', '未知错误')}"}
    except Exception as e:
        logger.error(f"❌ 内容安全检测异常: {e}")
        return {"success": False, "message": f"检测异常: {str(e)}"}

def ensure_public_url(image_url: str) -> str:
    """确保图片URL可以被微信服务器访问到，转换相对路径为绝对路径"""
    if image_url.startswith("http"):
        return image_url
    
    # 如果是相对路径，转换为绝对路径
    if not image_url.startswith("/"):
        image_url = f"/{image_url}"
    
    return f"{Config.BASE_URL}{image_url}"

@router.post("/callback")
async def handle_security_callback(request: Request):
    """接收微信内容安全检测的异步回调"""
    try:
        body = await request.json()
        logger.info(f"收到安全检测回调: {body}")
        
        # 验证是否是安全检测的事件
        if (body.get("MsgType") != "event" or 
            body.get("Event") != "wxa_media_check"):
            return {"code": 0, "message": "非内容安全事件"}
        
        trace_id = body.get("trace_id")
        is_risky = body.get("isrisky", 0)  # 0表示安全，1表示风险
        status_code = body.get("status_code", 0)
        
        if status_code != 0:
            logger.error(f"安全检测失败，status_code: {status_code}, trace_id: {trace_id}")
            return {"code": 0, "message": "检测失败"}
        
        # 查找对应的图片URL
        if trace_id in _pending_checks:
            image_url = _pending_checks[trace_id]["image_url"]
            
            # 更新检测结果缓存
            _image_check_results[image_url] = {
                "result": is_risky,
                "timestamp": time.time()
            }
            
            # 清理待处理任务
            _pending_checks.pop(trace_id, None)

            # 根据风险等级记录不同级别的日志
            if is_risky == 1:
                logger.warning(f"⚠️ 检测到风险图片: {image_url}, trace_id: {trace_id}")
                # 这里可以添加其他处理逻辑，例如将结果写入数据库或发送警报
            else:
                logger.debug(f"图片安全检测通过: {image_url}, trace_id: {trace_id}")
            
        else:
            logger.warning(f"收到未知trace_id的回调: {trace_id}")
        
        return {"code": 0, "message": "成功处理"}
    except Exception as e:
        logger.error(f"处理安全检测回调异常: {e}")
        return {"code": -1, "message": f"处理异常: {str(e)}"}

async def is_image_safe(image_url: str) -> bool:
    """
    检查图片是否安全，用于在展示给用户前进行检测
    
    Args:
        image_url: 图片URL
        
    Returns:
        bool: 图片是否安全，True=安全，False=不安全或未知
    """
    # 首先检查缓存中是否有结果
    if image_url in _image_check_results:
        cache_data = _image_check_results[image_url]
        # 如果缓存有效且有明确结果
        if time.time() - cache_data["timestamp"] < 86400 and cache_data["result"] is not None:
            return cache_data["result"] == 0  # 0表示安全
    
    # 如果没有缓存结果，提交检测
    check_result = await check_image_security(image_url)
    
    # 如果从缓存获取到了结果
    if check_result.get("from_cache") and check_result.get("result") is not None:
        return check_result["result"] == 0
    
    # 如果是新提交的检测，默认为安全
    # 因为微信是异步回调，我们无法立即获取结果
    # 实际应用中，可以考虑在首次生成时就提交检测，并在用户查看时再次检查结果
    logger.debug(f"图片 {image_url} 安全检测已提交，但结果未知，默认为安全")
    return True

# 清理过期的缓存数据
def cleanup_expired_data():
    """清理过期的缓存数据和待处理任务"""
    current_time = time.time()
    
    # 清理24小时前的缓存结果
    expired_urls = [url for url, data in _image_check_results.items() 
                   if current_time - data["timestamp"] > 86400]
    for url in expired_urls:
        _image_check_results.pop(url, None)
    
    # 清理1小时前仍未收到回调的待处理任务
    expired_traces = [tid for tid, data in _pending_checks.items() 
                     if current_time - data["timestamp"] > 3600]
    for tid in expired_traces:
        _pending_checks.pop(tid, None)
    
    if expired_urls or expired_traces:
        logger.info(f"清理了 {len(expired_urls)} 个过期缓存和 {len(expired_traces)} 个过期任务") 