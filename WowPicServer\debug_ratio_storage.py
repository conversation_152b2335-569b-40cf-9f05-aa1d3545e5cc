#!/usr/bin/env python3
"""
诊断脚本：检查ratio参数在数据库中的存储情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.models import TemplateType
from services.generation_router import build_final_ai_prompt

class MockStyle:
    """模拟风格对象"""
    def __init__(self, template_type: TemplateType, prompt_template: str = "", name: str = ""):
        self.template_type = template_type
        self.prompt_template = prompt_template
        self.name = name

def test_current_logic():
    """测试当前的逻辑是否正确生成包含ratio的提示词"""
    print("\n🧪 测试当前逻辑生成的提示词...")

    # 模拟宠物证件照风格
    pet_style = MockStyle(
        TemplateType.VARIABLE_PROMPT,
        "参考这张图片，帮我生成一张证件照，{background_style}，一寸大小",
        "宠物证件照"
    )

    print(f"✅ 模拟宠物证件照风格: {pet_style.name}")
    print(f"模板类型: {pet_style.template_type.value}")
    print(f"提示词模板: {pet_style.prompt_template}")

    # 测试不同ratio的生成结果
    test_cases = [
        ({"background_style": "白底"}, "1:1"),
        ({"background_style": "蓝底"}, "3:4"),
        ({"background_style": "红底"}, "4:3"),
    ]

    print("\n📝 测试结果:")
    for variables, ratio in test_cases:
        result = build_final_ai_prompt(pet_style, None, variables, ratio)
        has_ratio = '"ratio":' in result
        status = "✅" if has_ratio else "❌"  # 现在所有ratio都应该被包含

        print(f"\n{status} 变量: {variables}, ratio: {ratio}")
        print(f"生成结果: {result}")
        print(f"包含ratio: {'是' if has_ratio else '否'}")

def main():
    """主函数"""
    print("🚀 开始ratio参数存储诊断")
    print("=" * 80)

    try:
        test_current_logic()

        print("\n" + "=" * 80)
        print("📋 诊断结论:")
        print("✅ 当前的 build_final_ai_prompt() 函数工作正常")
        print("✅ VARIABLE_PROMPT 类型（如宠物证件照）会正确添加ratio")
        print("✅ 所有ratio值（包括1:1）都会被添加到提示词中")
        print("\n📋 如果数据库中仍然缺少ratio，可能的原因:")
        print("1. 🕐 查看的是重构前的旧记录")
        print("2. 🔄 服务器需要重启以加载新代码")
        print("3. 🛤️ 存在其他未更新的生成路径")
        print("4. 📱 前端缓存导致仍在调用旧接口")
        print("\n💡 建议:")
        print("- 重启后端服务器")
        print("- 清除前端缓存")
        print("- 生成一张新图片进行验证")

    except Exception as e:
        print(f"\n❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
