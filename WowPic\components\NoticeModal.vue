<template>
	<!-- 公告弹窗组件 -->
	<view class="notice-modal" v-if="show && noticeData">
		<view class="notice-overlay" @click="close"></view>
		<view class="notice-content">
			<!-- 装饰元素 -->
			<view class="notice-decoration notice-decoration-1"></view>
			<view class="notice-decoration notice-decoration-2"></view>
			
			<!-- 公告头部 -->
			<view class="notice-header">
				<text class="notice-title">{{ noticeData.title }}</text>
				<view class="notice-close" @click="close">×</view>
			</view>
			
			<!-- 公告内容 -->
			<scroll-view class="notice-body" scroll-y="true">
				<!-- 图片轮播 -->
				<swiper v-if="noticeData.images && noticeData.images.length > 0" 
					class="notice-swiper"
					:indicator-dots="noticeData.images.length > 1" 
					:autoplay="true" 
					:interval="3000" 
					:duration="500"
					indicator-color="rgba(255,255,255,0.6)"
					indicator-active-color="#FFFFFF">
					<swiper-item v-for="(img, index) in noticeData.images" :key="index">
						<view class="notice-image-container">
							<image class="notice-image" :src="getImageUrl(img)" mode="aspectFill" @click="previewImage(img)"></image>
						</view>
					</swiper-item>
				</swiper>
				
				<!-- 文本内容 -->
				<view class="notice-text">{{ noticeData.content }}</view>
			</scroll-view>
			
			<!-- 公告底部 -->
			<view class="notice-footer">
				<view class="notice-btn" @click="close" :class="{'btn-pressed': isButtonPressed}">
					<text class="btn-text">{{ noticeData.button_text || '知道了' }}</text>
					<view class="btn-sparkle"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getImageUrl } from '../utils/config.js'
	
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			noticeData: {
				type: Object,
				default: null
			}
		},
		data() {
			return {
				isButtonPressed: false // 控制公告弹窗关闭按钮的点击状态
			}
		},
		methods: {
			// 处理图片URL
			getImageUrl(url) {
				return getImageUrl(url);
			},
			
			// 关闭公告
			close() {
				// 设置按钮状态为按下
				this.isButtonPressed = true;
				
				// 短暂延迟后恢复按钮状态，模拟弹起效果
				setTimeout(() => {
					this.isButtonPressed = false;
					
					// 按钮弹起效果展示后，再延迟关闭弹窗
					setTimeout(() => {
						this.$emit('close', this.noticeData.id);
					}, 300);
				}, 150);
			},
			
			// 预览公告图片
			previewImage(current) {
				if (!this.noticeData || !this.noticeData.images || this.noticeData.images.length === 0) return;
				
				const urls = this.noticeData.images.map(img => this.getImageUrl(img));
				uni.previewImage({
					urls: urls,
					current: this.getImageUrl(current)
				});
			}
		}
	}
</script>

<style>
	/* 公告弹窗样式 - 动漫风格 */
	.notice-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.notice-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		backdrop-filter: blur(4px);
		z-index: -1;
		animation: fade-in 0.4s ease;
	}
	
	@keyframes fade-in {
		from { opacity: 0; }
		to { opacity: 1; }
	}
	
	.notice-content {
		width: 85%;
		max-width: 650rpx;
		max-height: 80vh;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		/* 增强动漫风格的线条边框 */
		border: 8rpx solid #333;
		box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.25);
		overflow: hidden;
		display: flex;
		flex-direction: column;
		animation: notice-popup 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		position: relative;
		/* 添加卡通感的白色内描边 */
		box-sizing: border-box;
	}
	
	/* 装饰元素 - 角落圆形 */
	.notice-decoration {
		position: absolute;
		border-radius: 50%;
		z-index: 1;
	}
	
	.notice-decoration-1 {
		top: -30rpx;
		left: -30rpx;
		width: 120rpx;
		height: 120rpx;
		background: linear-gradient(135deg, rgba(74, 144, 226, 0.2) 0%, rgba(117, 98, 255, 0.05) 100%);
		animation: float-animation 4s ease-in-out infinite;
	}
	
	.notice-decoration-2 {
		bottom: -40rpx;
		right: -40rpx;
		width: 150rpx;
		height: 150rpx;
		background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 153, 102, 0.05) 100%);
		animation: float-animation 4.5s ease-in-out infinite reverse;
	}
	
	@keyframes float-animation {
		0% { transform: translateY(0) rotate(0); }
		50% { transform: translateY(-10rpx) rotate(5deg); }
		100% { transform: translateY(0) rotate(0); }
	}
	
	@keyframes notice-popup {
		0% {
			transform: scale(0.7);
			opacity: 0;
		}
		40% {
			transform: scale(1.05);
		}
		70% {
			transform: scale(0.95);
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}
	
	.notice-header {
		position: relative;
		padding: 24rpx 30rpx;
		border-bottom: 3rpx solid #333;
		text-align: center;
		background-color: #f8f9fa;
		/* 添加微妙的纹理 */
		background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
	}
	
	.notice-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		/* 标题强制居中 */
		display: block;
		width: 100%;
		text-align: center;
		padding: 0 60rpx; /* 两侧预留空间给关闭按钮 */
		box-sizing: border-box;
		/* 添加文字阴影增强立体感 */
		text-shadow: 1rpx 1rpx 0 rgba(255,255,255,0.8), -1rpx -1rpx 0 rgba(0,0,0,0.1);
	}
	
	.notice-close {
		position: absolute;
		right: 24rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 48rpx;
		color: #333;
		border-radius: 50%;
		/* 添加动漫风格的边框和背景 */
		border: 3rpx solid #333;
		background-color: #fff;
		box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
		/* 添加按钮动态效果 */
		transition: all 0.2s;
	}
	
	.notice-close:active {
		transform: translateY(-50%) scale(0.9);
		background-color: #f0f0f0;
		box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
	}
	
	.notice-body {
		flex: 1;
		max-height: 60vh;
		padding: 0;
		position: relative;
		z-index: 2;
	}
	
	.notice-swiper {
		width: 100%;
		height: 340rpx;
		position: relative;
		/* 添加边框增强卡通感 */
		border-bottom: 3rpx solid #333;
	}
	
	.notice-image-container {
		width: 100%;
		height: 400rpx; /* 增加图片高度 */
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		background-color: #f0f0f0;
	}
	
	.notice-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	
	.notice-text {
		padding: 30rpx;
		font-size: 28rpx;
		line-height: 1.6;
		color: #333333;
		white-space: pre-wrap;
		/* 添加有趣的段落缩进 */
		text-indent: 2em;
		/* 改进文本可读性 */
		letter-spacing: 1rpx;
	}
	
	.notice-footer {
		padding: 20rpx 30rpx 30rpx;
		display: flex;
		justify-content: center;
		position: relative;
		z-index: 2;
		background-color: #f8f9fa;
		border-top: 3rpx solid #f0f0f0;
	}
	
	.notice-btn {
		background: linear-gradient(135deg, #4A90E2, #7562FF);
		color: #FFFFFF;
		font-size: 30rpx;
		font-weight: bold;
		padding: 16rpx 0;
		border-radius: 40rpx;
		width: 70%;
		text-align: center;
		/* 增强动漫风格边框 */
		border: 4rpx solid #333;
		box-shadow: 0 8rpx 0 #333, 0 10rpx 16rpx rgba(0, 0, 0, 0.2);
		position: relative;
		/* 添加按下效果的过渡 */
		transition: all 0.1s ease;
		overflow: hidden;
	}
	
	/* 新增：按钮按下状态样式 */
	.btn-pressed {
		transform: translateY(6rpx);
		box-shadow: 0 2rpx 0 #333, 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	}
	
	.notice-btn:active {
		transform: translateY(6rpx);
		box-shadow: 0 2rpx 0 #333, 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	}
	
	/* 按钮内文字 */
	.btn-text {
		position: relative;
		z-index: 2;
		/* 添加文字阴影增强可读性 */
		text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
	}
	
	/* 按钮闪光效果 */
	.btn-sparkle {
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, 
			rgba(255,255,255,0) 0%, 
			rgba(255,255,255,0.2) 50%, 
			rgba(255,255,255,0) 100%
		);
		z-index: 1;
		animation: btn-sparkle 2.5s ease-in-out infinite;
	}
	
	@keyframes btn-sparkle {
		0% { left: -100%; }
		50% { left: 100%; }
		100% { left: 100%; }
	}
</style> 