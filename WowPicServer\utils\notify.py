import logging
import httpx
import time
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import re
import datetime
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from config import Config
from utils.auth import get_current_user, get_db
from database.models import UserAuthentication

# >>> 新增: 硬编码微信订阅消息模板ID <<<
TEMPLATE_ID = "rCt4O5tjTD7QOSLh13fgV0PyOv592orclKP_M2h8pbI"  # TODO: 替换为真实的模板ID

# 支持的默认兜底短语，确保在微信短语词库常见范围
_DEFAULT_PHRASE = "AI绘画"

_CN_RE = re.compile(r"[\u4e00-\u9fff]+")

# 匹配 HH:MM 或 HH:MM:SS
_TIME_RE = re.compile(r"(\d{1,2}:\d{2}(?::\d{2})?)")

# -------- access_token 本地缓存 ---------
# key = appid, value = (token, expire_ts)
_access_token_cache: dict[str, tuple[str, float]] = {}
# 提前 10 分钟刷新，微信默认7200s 过期
_TOKEN_GUARD_SECONDS = 600
# ----------------------------------------


def _sanitize_phrase_value(value: str) -> str:
    """尝试将 phrase 数据字段修正为符合微信要求的 2~5 个中文字符。

    规则：
    1. 仅提取中文字符
    2. 去掉常见无效后缀，如“风格”“效果”等
    3. 最终长度不超过 5 个中文字符；不足 2 则使用默认词
    """
    if not value:
        return _DEFAULT_PHRASE

    # 提取中文
    chinese_only = "".join(_CN_RE.findall(value))
    # 去除无意义后缀
    for suffix in ("风格", "效果", "主题"):
        if chinese_only.endswith(suffix):
            chinese_only = chinese_only[: -len(suffix)]
    # 截断到 5 个字
    chinese_only = chinese_only[:5]
    # 微信 phrase 类型通常要求 2~5 个汉字，做最小长度校验
    if len(chinese_only) < 2:
        return _DEFAULT_PHRASE
    return chinese_only


def _sanitize_time_value(value: str) -> str:
    """确保时间字段仅包含 HH:MM 或 HH:MM:SS 格式。若未匹配到，返回当前 HH:MM。"""
    if not value:
        return datetime.datetime.now().strftime("%H:%M")
    m = _TIME_RE.search(value)
    if m:
        return m.group(1)
    return datetime.datetime.now().strftime("%H:%M")


notify_router = APIRouter(prefix="/wowpic/notify", tags=["订阅消息"])
logger = logging.getLogger(__name__)

async def _get_access_token() -> str:
    """获取小程序全局 access_token"""
    # 1. 命中缓存且未过期
    cached = _access_token_cache.get(Config.WX_APPID)
    if cached and cached[1] > time.time():
        remaining_time = int(cached[1] - time.time())
        logger.debug(f"使用缓存的access_token，剩余有效期: {remaining_time}秒")
        return cached[0]

    # 2. 重新拉取
    logger.info(f"开始获取新的access_token，appid: {Config.WX_APPID}")

    async with httpx.AsyncClient(timeout=10.0) as client:
        resp = await client.get(
            "https://api.weixin.qq.com/cgi-bin/token",
            params={
                "grant_type": "client_credential",
                "appid": Config.WX_APPID,
                "secret": Config.WX_SECRET,
            },
        )
        resp.raise_for_status()
        data = resp.json()

        # 记录微信API的完整响应
        logger.info(f"微信access_token API响应: {data}")

        if "access_token" not in data:
            error_msg = data.get("errmsg", "未知错误")
            error_code = data.get("errcode", 0)
            logger.error(f"❌ 获取access_token失败: error_code={error_code}, error_msg={error_msg}, 完整响应: {data}")
            raise RuntimeError(f"获取 access_token 失败: {data}")

        token = data["access_token"]
        expires_in = int(data.get("expires_in", 7200))
        # 计算提前刷新时间
        expire_ts = time.time() + max(60, expires_in - _TOKEN_GUARD_SECONDS)
        _access_token_cache[Config.WX_APPID] = (token, expire_ts)

        expire_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expire_ts))
        logger.info(f"✅ 成功获取access_token，有效期: {expires_in}秒，缓存到: {expire_time_str}")
        return token

@notify_router.post("/send", summary="发送微信订阅消息通知")
async def send_subscribe_message(
    payload: dict,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user),
):
    """发送订阅消息

    前端需提供：
    {
        templateId: str,   # 微信订阅消息模板ID
        data: dict,        # 订阅消息数据
        page: str,         # 跳转页面路径，默认首页
        generationId: int  # 可选，方便后续扩展
    }
    """
    # 优先使用前端传入，其次使用硬编码模板ID
    template_id = payload.get("templateId") or TEMPLATE_ID
    data = payload.get("data")
    page = payload.get("page", "pages/index/index")

    if not template_id or not data:
        raise HTTPException(status_code=400, detail="参数缺失")

    # ---- 对 phrase 类型字段进行自动校验与修正 ----
    for k, v in list(data.items()):
        if not (isinstance(v, dict) and "value" in v):
            continue
        if k.startswith("phrase"):
            v["value"] = _sanitize_phrase_value(str(v["value"]))
        elif k.startswith("time"):
            v["value"] = _sanitize_time_value(str(v["value"]))

    # 获取用户 openid（仅示例：平台=WX 的第一条记录）
    auth = (
        db.query(UserAuthentication)
        .filter(
            UserAuthentication.user_id == current_user.id,
            UserAuthentication.platform == "WX",
        )
        .first()
    )
    if not auth:
        raise HTTPException(status_code=400, detail="用户未绑定微信 OpenID")

    try:
        access_token = await _get_access_token()
    except Exception as e:
        logger.error(f"获取 access_token 失败: {e}")
        raise HTTPException(status_code=500, detail="获取access_token失败")

    body = {
        "touser": auth.platform_uid,
        "template_id": template_id,
        "page": page,
        "data": data,
        "miniprogram_state": "formal",
    }

    async with httpx.AsyncClient(timeout=10.0) as client:
        # 记录发送请求的详细信息
        logger.info(f"开始发送订阅消息: user_id={current_user.id}, openid={auth.platform_uid}, template_id={template_id}")

        # 先尝试发送一次
        resp = await client.post(
            "https://api.weixin.qq.com/cgi-bin/message/subscribe/send",
            params={"access_token": access_token},
            json=body,
        )
        resp.raise_for_status()
        result = resp.json()

        # 记录第一次发送的结果
        logger.info(f"订阅消息第一次发送结果: {result}")

        # 如果因 phrase value 不合法导致 47003，则兜底重试一次
        if result.get("errcode") == 47003 and "phrase" in result.get("errmsg", ""):
            logger.warning(f"订阅消息因phrase字段不合法被拒绝，准备重试: {result}")

            # 将所有 phrase 字段替换为默认值后重试一次
            for k, v in data.items():
                if k.startswith("phrase") and isinstance(v, dict):
                    v["value"] = _DEFAULT_PHRASE
            body["data"] = data

            logger.info(f"重试发送订阅消息，已替换phrase字段为默认值: {_DEFAULT_PHRASE}")

            resp = await client.post(
                "https://api.weixin.qq.com/cgi-bin/message/subscribe/send",
                params={"access_token": access_token},
                json=body,
            )
            resp.raise_for_status()
            result = resp.json()

            # 记录重试的结果
            logger.info(f"订阅消息重试发送结果: {result}")

    # 检查最终结果
    if result.get("errcode", 0) != 0:
        # 详细记录失败信息
        error_msg = result.get("errmsg", "未知错误")
        error_code = result.get("errcode", 0)
        logger.error(f"❌ 订阅消息发送失败: user_id={current_user.id}, openid={auth.platform_uid}, "
                    f"error_code={error_code}, error_msg={error_msg}, "
                    f"template_id={template_id}, 完整响应: {result}")
        raise HTTPException(status_code=500, detail="订阅消息发送失败")
    else:
        # 记录成功信息
        logger.info(f"✅ 订阅消息发送成功: user_id={current_user.id}, openid={auth.platform_uid}, "
                   f"template_id={template_id}, msgid={result.get('msgid', 'N/A')}")

    return {"success": True}

@notify_router.get("/template-id", summary="获取订阅消息模板ID")
async def get_template_id():
    """前端调用此接口即可获取当前配置的订阅消息模板ID"""

    if not TEMPLATE_ID:
        raise HTTPException(status_code=404, detail="未配置订阅消息模板ID")
    return {"templateId": TEMPLATE_ID}

# 创建公告路由器
notices_router = APIRouter(prefix="/wowpic/notices", tags=["公告"])

# 公告配置
NOTICES = {
    "enabled": True,  # 是否启用公告
    "notices": [  # 支持多个公告
        {
            "id": "notice_002",  # 公告ID
            "title": "名人合照风格使用须知",  # 公告标题
            "content": "【名人合照风格重要提示】\n\n- 所有照片均由AI技术生成。\n- 全球知名明星（如刘亦菲、爱因斯坦等）可直接输入名字。\n- 其他名人强烈建议上传正脸清晰照作为参考照片。\n\nAI生成结果不可控，没有任何人可以确保最终生成效果。请理性消费！！！",  # 公告内容
            "images": [  # 公告图片列表
                "/static/banners/banner2.jpg",
                "/static/banners/banner3.jpg"
            ],
            "created_at": int(time.time()),  # 创建时间戳
            "expire_at": int(time.time()) + 7 * 86400,  # 过期时间戳（7天后）
            "button_text": "知道了",  # 按钮文本
            "target_styles": ["celebrity_selfie_v1"],  # 关联的风格model_identifier列表，空数组表示在首页显示
            "display_type": "style_page"  # 显示类型：home_page（首页）或 style_page（风格页面）
        }
        # {
        #     "id": "notice_003",  # 新增示例公告
        #     "title": "欢迎使用哇图AI",
        #     "content": "🎉 欢迎来到哇图AI！\n\n✨ 上传你的照片，选择喜欢的风格\n🎨 AI将为你创造独特的艺术作品\n💎 每次生成仅需少量哇图币\n\n开始你的创作之旅吧！",
        #     "images": [],
        #     "created_at": int(time.time()),
        #     "expire_at": int(time.time()) + 30 * 86400,  # 30天后过期
        #     "button_text": "开始创作",
        #     "target_styles": [],  # 空数组表示在首页显示
        #     "display_type": "home_page"
        # }
    ]
}

# 获取当前公告的模型
class NoticeResponse(BaseModel):
    has_notice: bool
    notice: Optional[Dict[str, Any]] = None

# 获取公告接口
@notices_router.get("", response_model=NoticeResponse)
async def get_current_notice(
    display_type: str = "home_page",  # 显示类型：home_page 或 style_page
    style_identifier: Optional[str] = None  # 风格标识符，仅在 style_page 时需要
):
    """获取当前有效的公告

    Args:
        display_type: 显示类型，home_page（首页）或 style_page（风格页面）
        style_identifier: 风格的model_identifier，仅在display_type为style_page时需要
    """
    if not NOTICES["enabled"]:
        return {"has_notice": False, "notice": None}

    current_time = int(time.time())

    # 遍历所有公告，找到符合条件的第一个
    for notice in NOTICES["notices"]:
        # 检查公告是否过期
        if "expire_at" in notice and notice["expire_at"] < current_time:
            continue

        # 检查显示类型
        if notice.get("display_type", "home_page") != display_type:
            continue

        # 如果是风格页面，检查风格匹配
        if display_type == "style_page":
            if not style_identifier:
                continue

            target_styles = notice.get("target_styles", [])
            # 如果target_styles为空，表示不在风格页面显示
            if not target_styles:
                continue

            # 检查当前风格是否在目标风格列表中
            if style_identifier not in target_styles:
                continue

        # 如果是首页，检查是否为首页公告
        elif display_type == "home_page":
            target_styles = notice.get("target_styles", [])
            # 只有target_styles为空的公告才在首页显示
            if target_styles:
                continue

        # 找到符合条件的公告
        return {"has_notice": True, "notice": notice}

    # 没有找到符合条件的公告
    return {"has_notice": False, "notice": None}

# 您可以根据需要添加修改公告的接口，或者直接在代码中修改NOTICES字典 

# === 聚合路由，供外部导入 ===
router = APIRouter()
router.include_router(notify_router)
router.include_router(notices_router) 