import os
from dotenv import load_dotenv
from pathlib import Path

# ------------------------------------
# 首先定位到项目根目录，再显式加载.env
# ------------------------------------
# 计算当前文件所在目录作为项目根目录
BASE_DIR = Path(__file__).resolve().parent

# 显式指定.env文件路径，确保无论工作目录如何都能正确加载
load_dotenv(dotenv_path=BASE_DIR / '.env', override=False)

# ---- 以下保持不变 ----

class Config:
    # 数据库配置
    DB_HOST = os.getenv('DB_HOST', 'localhost')
    DB_PORT = int(os.getenv('DB_PORT', 3306))
    DB_USER = os.getenv('DB_USER', 'root')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_NAME = os.getenv('DB_NAME', 'wowpic')
    
    # 构建数据库URL
    DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"
    
    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    JWT_ALGORITHM = os.getenv('JWT_ALGORITHM', 'HS256')
    JWT_EXPIRE_HOURS = int(os.getenv('JWT_EXPIRE_HOURS', 24))
     
    # Redis配置
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
    REDIS_DB = int(os.getenv('REDIS_DB', 0))

    # 应用配置
    APP_HOST = os.getenv('APP_HOST', 'localhost')
    APP_PORT = int(os.getenv('APP_PORT', 8000))
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'

    # 基础URL，用于构建完整的文件URL（必须放在最前，供后续配置引用）
    BASE_URL = os.getenv('BASE_URL', f"http://{APP_HOST}:{APP_PORT}")
    
    # 数据库自动更新配置
    AUTO_UPDATE_DB_SCHEMA = os.getenv('AUTO_UPDATE_DB_SCHEMA', 'False').lower() == 'true'
    
    # 微信小程序配置
    WX_APPID = os.getenv('WX_APPID', 'wxb703827dbabc44f5')  # 使用.env中的配置
    WX_SECRET = os.getenv('WX_SECRET', 'a4752bd6e7d6503dbcc4d8a8f3554515')  # 使用.env中的配置

    # 微信内容安全检测配置
    WX_CONTENT_SECURITY_ENABLED = os.getenv('WX_CONTENT_SECURITY_ENABLED', 'True').lower() == 'true'
    WX_CONTENT_SECURITY_CALLBACK_PATH = os.getenv('WX_CONTENT_SECURITY_CALLBACK_PATH', '/wowpic/security/callback')

    # ---- 微信支付 (V3) ----
    # 商户号
    WECHAT_MCH_ID = os.getenv('WECHAT_MCH_ID', '')
    # 商户API证书序列号
    WECHAT_MCH_SERIAL_NO = os.getenv('WECHAT_MCH_SERIAL_NO', '')
    # APIv3密钥（32位字符）
    WECHAT_API_V3_KEY = os.getenv('WECHAT_API_V3_KEY', '')
    # 商户私钥文件路径（pem）
    WECHAT_PRIVATE_KEY_PATH = os.getenv('WECHAT_PRIVATE_KEY_PATH', 'apiclient_key.pem')

    # 支付成功回调地址（绝对URL）
    WECHAT_PAY_NOTIFY_URL = os.getenv('WECHAT_PAY_NOTIFY_URL', f"{BASE_URL}/wowpic/pay/notify")
    
    # 订阅消息模板ID（如需前端使用，可通过接口返回）
    WX_SUBSCRIBE_TEMPLATE_ID = os.getenv('WX_SUBSCRIBE_TEMPLATE_ID', '')
    
    AIHUBMIX_API_KEY = os.getenv('AIHUBMIX_API_KEY', '')
    
    # 文件上传配置
    UPLOAD_DIR = os.getenv('UPLOAD_DIR', str(BASE_DIR / 'static' / 'uploads'))
    
    # 静态文件目录路径
    STATIC_PATH = os.getenv('STATIC_PATH', str(BASE_DIR / 'static'))