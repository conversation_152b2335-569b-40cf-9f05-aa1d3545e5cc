# WowPic Banner 系统开发计划

## 1. 功能概述

为了提升应用内容的多样性和运营能力，本项目需要开发一个首页 Banner 系统。此系统允许在后端代码中直接配置 Banner 内容，每个 Banner 支持独立的点击行为，例如跳转到指定页面或给予用户哇图币奖励。

该功能的核心在于**简便、快速实现**，因此不涉及独立的数据库表和后台管理界面，所有配置直接在后端代码中完成。

## 2. 后端开发计划 (FastAPI)

### 2.1. Banner 数据结构设计

我们将在后端的 Banner 路由文件中直接硬编码定义一个 Banner 列表，以最大限度简化项目结构。

每个 Banner 对象包含以下字段：

- `id`: (Integer) 唯一标识符。
- `image_url`: (String) Banner 图片的 URL。建议存放于 `WowPicServer/static/banners/` 目录下。
- `action`: (Object) 点击后触发的动作。
    - `type`: (String) 动作类型，如 `NAVIGATE` (跳转页面) 或 `REWARD_COINS` (奖励哇图币)。
    - `payload`: (Object) 动作相关的参数。
        - 对于 `NAVIGATE`: `{ "url": "/pages/some/page" }`
        - 对于 `REWARD_COINS`: `{ "amount": 10, "message": "恭喜获得10哇图币！" }`

**示例 (在 `wowpic/routers/banner.py` 文件顶部定义):**

```python
# Banner 配置直接定义在路由文件中
BANNERS = [
    {
        "id": 1,
        "image_url": "/static/banners/banner1.png",
        "action": {
            "type": "NAVIGATE",
            "payload": {
                "url": "/pages/generate/generate?style=名人合照"
            }
        }
    },
    {
        "id": 2,
        "image_url": "/static/banners/banner2.jpg",
        "action": {
            "type": "REWARD_COINS",
            "payload": {
                "amount": 10,
                "message": "每日福利！恭喜您获得10哇图币！"
            }
        }
    },
    {
        "id": 3,
        "image_url": "/static/banners/banner3.jpg",
        "action": {
            "type": "NONE" # 无操作
        }
    }
]
```

### 2.2. API 接口设计

需要创建两个新的 API 接口。建议新建一个 `wowpic/routers/banner.py` 文件来管理相关路由。

1.  **获取 Banner 列表**
    - **Endpoint**: `GET /wowpic/banners`
    - **描述**: 返回所有可用的 Banner 配置列表。
    - **请求**: 无
    - **成功响应 (200 OK)**:
        ```json
        [
            {
                "id": 1,
                "image_url": "http://localhost:8000/static/banners/banner1.png"
            },
            {
                "id": 2,
                "image_url": "http://localhost:8000/static/banners/banner2.jpg"
            }
        ]
        ```
    - **注意**: 为安全起见，此接口不返回 `action` 详细信息。

2.  **处理 Banner 点击**
    - **Endpoint**: `POST /wowpic/banners/{banner_id}/click`
    - **描述**: 用户点击某个 Banner 后，前端调用此接口以触发后端逻辑。
    - **认证**: 需要用户 Token。
    - **请求**: 无
    - **成功响应 (200 OK)**:
        - 返回该 Banner 对应的 `action`，前端根据此响应执行相应操作。
        - 如果是奖励哇图币，后端直接处理，并返回 `action` 以便前端提示。
        ```json
        {
            "type": "NAVIGATE",
            "payload": {
                "url": "/pages/generate/generate?style=名人合照"
            }
        }
        ```
        或者
        ```json
        {
            "type": "REWARD_COINS",
            "payload": {
                "amount": 10,
                "message": "恭喜获得10哇图币！"
            }
        }
        ```

### 2.3. 业务逻辑实现

1.  **创建 `wowpic/routers/banner.py`**:
    - 在文件顶部，直接定义 `BANNERS` 列表（如 2.1 所示）。
    - 实现 `GET /wowpic/banners` 接口，读取本文件中的 `BANNERS` 列表，处理 `image_url` (拼接完整路径)，并返回。
    - 实现 `POST /wowpic/banners/{banner_id}/click` 接口：
        - 依赖 `get_current_user` 获取当前用户。
        - 根据 `banner_id` 查找对应的 Banner 配置。
        - 如果 `action.type` 是 `REWARD_COINS`，则更新 `users` 表中当前用户的 `coins` 字段。需要注意防止用户重复点击刷取（可考虑 Redis 记录用户当天是否已点击）。
        - 返回完整的 `action` 对象给前端。
2.  **更新 `main.py`**:
    - 导入并注册 `banner` 路由。

## 3. 前端开发计划 (uni-app)

### 3.1. UI 组件实现

在 `pages/index/index.vue` 中添加一个 `swiper` 组件用于轮播展示 Banner。

```html
<!-- 轮播图 Banner -->
<view class="banner-section">
    <swiper class="banner-swiper" circular="true" autoplay="true" interval="3000" duration="500">
        <swiper-item v-for="banner in bannerList" :key="banner.id" @click="onBannerClick(banner.id)">
            <image class="banner-image" :src="banner.image_url" mode="aspectFill"></image>
        </swiper-item>
    </swiper>
</view>
```

### 3.2. 数据获取与展示

1.  在 `pages/index/index.vue` 的 `data` 中添加 `bannerList: []`。
2.  在 `onLoad` 或 `onShow`生命周期中，调用 `request.get('/wowpic/banners')` 获取 Banner 数据。
3.  将获取的数据赋值给 `this.bannerList`。

### 3.3. 点击事件处理

1.  **创建 `onBannerClick(bannerId)` 方法**:
    - 调用 `request.post('/wowpic/banners/' + bannerId + '/click')`。
    - 显示 `uni.showLoading` 以提升用户体验。

2.  **处理后端响应**:
    - 在 `.then(res => { ... })` 中，获取到 `action` 对象。
    - 使用 `if/else` 或 `switch` 判断 `action.type`：
        - **`NAVIGATE`**: 调用 `uni.navigateTo({ url: action.payload.url })`。
        - **`REWARD_COINS`**: 调用 `uni.showToast({ title: action.payload.message })` 给予用户提示。同时，需要重新调用 `loadUserInfo()` 方法来刷新界面上显示的哇图币数量。
        - **`NONE`**: 不执行任何操作。

## 4. 开发步骤概要

1.  **后端**: 在 `WowPicServer` 目录下创建 `static/banners` 目录并放入示例图片。
2.  **后端**: 创建 `wowpic/routers/banner.py`，在其中定义 Banner 数据并实现 `GET /banners` 和 `POST /banners/{id}/click` 两个 API。
3.  **后端**: 在 `main.py` 中引入并注册 Banner 路由。
4.  **前端**: 在 `pages/index/index.vue` 中添加 `swiper` 组件和对应样式。
5.  **前端**: 在 `script` 部分添加获取 Banner 列表的逻辑。
6.  **前端**: 实现 `onBannerClick` 方法，完成点击事件的请求发送和响应处理。
7.  **联调**: 启动前后端服务，进行完整功能测试。

通过以上步骤，即可在不增加项目复杂度的情况下，快速、高效地完成 Banner 系统的开发。 