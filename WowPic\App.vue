<script>
	import { baseUrl } from './utils/config.js';
	
	export default {
		globalData: {
			apiBaseUrl: baseUrl,  // 使用 config.js 中的配置
			userInfo: null,
			isLoggedIn: false,
			loginPromise: null, // 全局登录Promise
			loginResolve: null, // 登录完成的resolve函数
			loginReject: null,   // 登录失败的reject函数
			shownNotices: []    // 当前会话已显示的公告ID列表
		},
		onLaunch: function() {
			console.log('App Launch')
			// 创建全局登录Promise
			this.globalData.loginPromise = new Promise((resolve, reject) => {
				this.globalData.loginResolve = resolve
				this.globalData.loginReject = reject
			})
			// 调用登录接口获取Token
			this.autoLogin()
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			autoLogin() {
				// 检查是否有登录Token
				const token = uni.getStorageSync('token')
				if (token) {
					// 如果已有Token，标记为已登录
					this.globalData.isLoggedIn = true
					console.log('已有登录凭证')
					// 通知登录已完成
					this.globalData.loginResolve({ success: true, isTokenReused: true })
					return
				}
				
				// 调用微信登录
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						if (loginRes.code) {
							// 获取到微信code，调用后端登录接口
							uni.request({
								url: `${this.globalData.apiBaseUrl}/wowpic/auth/login`,
								method: 'POST',
								data: {
									code: loginRes.code,
									platform: 'WX'
								},
								success: (res) => {
									if (res.statusCode === 200 && res.data.token) {
										// 存储Token
										uni.setStorageSync('token', res.data.token)
										this.globalData.isLoggedIn = true
										console.log('✅ [前端] 登录成功', res.data.isNewUser ? '新用户' : '老用户')
										// 打印Token方便调试
										console.log('🔑 [前端] 获取到Token, 已存储:', res.data.token)
										// 通知登录已完成
										this.globalData.loginResolve({ success: true, isNewUser: res.data.isNewUser })
									} else {
										console.error('登录失败:', res.data)
										// 通知登录失败
										this.globalData.loginReject({ success: false, message: '登录请求返回错误' })
									}
								},
								fail: (err) => {
									console.error('登录请求失败:', err)
									// 通知登录失败
									this.globalData.loginReject({ success: false, message: '登录请求失败', error: err })
								}
							})
						} else {
							console.error('微信登录失败:', loginRes)
							// 通知登录失败
							this.globalData.loginReject({ success: false, message: '微信登录失败', error: loginRes })
						}
					},
					fail: (err) => {
						console.error('uni.login调用失败:', err)
						// 通知登录失败
						this.globalData.loginReject({ success: false, message: 'uni.login调用失败', error: err })
					}
				})
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	
	/* WebKit浏览器隐藏滚动条 */
	::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
		background: transparent;
	}
	
	/* 标准方法隐藏滚动条 - Firefox */
	page,
	scroll-view,
	view {
		scrollbar-width: none;
		-ms-overflow-style: none;
	}
	
	/* 处理uni-app特定元素 */
	uni-page-body,
	uni-page-wrapper,
	uni-page,
	uni-scroll-view {
		scrollbar-width: none;
		-ms-overflow-style: none;
	}
	
	/* 隐藏scroll-view的滚动条 */
	scroll-view::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	/* 针对可能出现滚动条的其他元素 */
	[scroll-y="true"]::-webkit-scrollbar,
	[overflow-y="auto"]::-webkit-scrollbar,
	[overflow-y="scroll"]::-webkit-scrollbar,
	.content-scroll::-webkit-scrollbar,
	.popup-scroll-content::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	/* 工具类 */
	.no-scrollbar {
		scrollbar-width: none;
		-ms-overflow-style: none;
	}
	
	.no-scrollbar::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	/* 页面容器通用设置 */
	.container, .profile-container, .generate-container {
		overflow-x: hidden;
		width: 100vw;
		box-sizing: border-box;
	}
	
	/* 禁止页面级滚动 */
	page {
		width: 100vw;
		height: 100vh;
		overflow: hidden;
	}
</style>
