# WowPic项目后续开发计划

## 一、图片生成功能架构设计

### 1. 后端架构设计

#### 1.1 核心组件设计

```mermaid
flowchart TD
    A[用户请求] --> B[FastAPI接口层]
    B --> C[任务分发器]
    C --> D{模型选择器}
    D --> E1[风格模型A]
    D --> E2[风格模型B]
    D --> E3[风格模型C]
    E1 --> F[结果处理器]
    E2 --> F
    E3 --> F
    F --> G[返回结果]
```

#### 1.2 目录结构设计

建议在WowPicServer中添加以下目录结构：

```
WowPicServer/
├── generation/                # 图片生成相关API
│   ├── __init__.py
│   ├── router.py             # 统一API入口
│   └── models.py             # 请求和响应模型
├── services/                      # 业务逻辑层
│   ├── __init__.py
│   ├── generation_service.py     # 通用生成逻辑
│   └── styles/                   # 各风格的具体实现
│       ├── __init__.py
│       ├── base_style.py         # 基础风格类
│       ├── ghibli_style.py       # 吉卜力风格
│       ├── cat_travel_style.py   # 猫咪去旅行
│       └── ...
├── tasks/                         # Celery异步任务
│   ├── __init__.py
│   └── generation_tasks.py       # 图片生成任务
└── models/                       # AI模型封装
    ├── __init__.py
    ├── model_manager.py          # 模型管理
    └── adapters/                 # 不同AI服务的适配器
        ├── __init__.py
        ├── local_model.py        # 本地模型
        ├── openai_adapter.py     # OpenAI API适配
        └── ...
```

### 2. 风格管理与扩展设计

#### 2.1 风格抽象接口设计

创建一个抽象基类`BaseStyle`，所有风格都继承自这个基类：

```python
# services/styles/base_style.py
from abc import ABC, abstractmethod

class BaseStyle(ABC):
    """所有风格的抽象基类"""
    
    @abstractmethod
    async def generate(self, params: dict) -> str:
        """生成图片的核心方法"""
        pass
        
    @abstractmethod
    def validate_params(self, params: dict) -> bool:
        """验证输入参数"""
        pass
        
    @property
    @abstractmethod
    def model_identifier(self) -> str:
        """获取使用的模型标识符"""
        pass
```

#### 2.2 具体风格实现示例

```python
# services/styles/ghibli_style.py
from .base_style import BaseStyle
from models.model_manager import get_model

class GhibliStyle(BaseStyle):
    """吉卜力风格实现"""
    
    def __init__(self):
        self._model = get_model("stable_diffusion")
    
    async def generate(self, params: dict) -> str:
        # 验证参数
        self.validate_params(params)
        
        # 构建提示词
        prompt = f"Studio Ghibli style, {params.get('prompt', '')}"
        
        # 调用模型生成
        image_path = await self._model.generate_image(
            prompt=prompt,
            negative_prompt=params.get('negative_prompt', ''),
            seed=params.get('seed', None),
            steps=params.get('steps', 30)
        )
        
        return image_path
        
    def validate_params(self, params: dict) -> bool:
        required = ['prompt']
        for param in required:
            if param not in params:
                raise ValueError(f"缺少必要参数: {param}")
        return True
        
    @property
    def model_identifier(self) -> str:
        return "stable_diffusion"
```

## 二、任务队列与异步处理

### 1. Celery任务配置

```python
# tasks/generation_tasks.py
from celery import Celery
from config import Config
from services.generation_service import process_generation
from database.models import Generation, GenerationStatus

# 初始化Celery
celery_app = Celery('wowpic',
                   broker=f'redis://{Config.REDIS_HOST}:{Config.REDIS_PORT}/{Config.REDIS_DB}',
                   backend=f'redis://{Config.REDIS_HOST}:{Config.REDIS_PORT}/{Config.REDIS_DB}')

@celery_app.task(bind=True)
def generate_image_task(self, generation_id: int):
    """异步生成图片任务"""
    try:
        # 处理生成请求
        result = process_generation(generation_id)
        return result
    except Exception as e:
        # 更新数据库状态为失败
        update_generation_status(generation_id, GenerationStatus.FAILED, str(e))
        raise
```

## 三、API接口设计

### 1. 图片生成API路由

```python
# api/generation/router.py
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from database.models import User, Generation, GenerationStatus
from utils.auth import get_current_user, get_db
from tasks.generation_tasks import generate_image_task

router = APIRouter(
    prefix="/wowpic/generate",
    tags=["图片生成"],
)

@router.post("/{style_id}")
async def generate_image(
    style_id: int,
    prompt: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    image: UploadFile = File(None)
):
    """创建图片生成任务"""
    # 查询风格
    style = db.query(Style).filter(Style.id == style_id).first()
    if not style:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="风格不存在"
        )
        
    # 检查用户哇图币
    if current_user.coins < style.cost:
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=f"哇图币不足，需要{style.cost}个哇图币"
        )
    
    # 扣除哇图币
    current_user.coins -= style.cost
    
    # 创建生成记录
    generation = Generation(
        user_id=current_user.id,
        style_id=style_id,
        prompt=prompt,
        status=GenerationStatus.PENDING,
        cost=style.cost
    )
    
    # 如果上传了图片，处理图片
    if image:
        upload_result = await upload_image(image)
        generation.source_image_url = upload_result['url']
    
    db.add(generation)
    db.commit()
    db.refresh(generation)
    
    # 启动异步任务
    task = generate_image_task.delay(generation.id)
    
    # 更新任务ID
    generation.task_id = task.id
    db.commit()
    
    return {
        "generation_id": generation.id,
        "task_id": task.id,
        "status": "pending"
    }

@router.get("/status/{generation_id}")
async def check_generation_status(
    generation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查图片生成状态"""
    generation = db.query(Generation).filter(
        Generation.id == generation_id,
        Generation.user_id == current_user.id
    ).first()
    
    if not generation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="生成记录不存在或无权访问"
        )
    
    return {
        "generation_id": generation.id,
        "status": generation.status.value,
        "created_at": generation.created_at,
        "completed_at": generation.completed_at,
        "generated_image_url": generation.generated_image_url if generation.status == GenerationStatus.SUCCESS else None,
        "error_message": generation.error_message if generation.status == GenerationStatus.FAILED else None
    }
```

## 四、开发计划时间表

### 阶段一：基础架构搭建（1-2周）

1. **任务队列配置**
   - 配置Celery与Redis连接
   - 创建基本任务框架
   - 实现任务状态更新机制

2. **模型接口设计**
   - 设计模型管理器
   - 创建外部API适配器
   - 实现本地模型支持（如有需要）

### 阶段二：风格实现（2-3周）

1. **基础风格类开发**
   - 实现BaseStyle抽象类
   - 创建StyleFactory工厂类
   - 构建风格注册机制

2. **初始风格实现**
   - 开发3-5个初始风格类
   - 为每个风格配置专属参数
   - 测试风格生成效果

### 阶段三：API接口完善（1-2周）

1. **生成接口开发**
   - 实现创建生成任务接口
   - 开发状态查询接口
   - 添加生成结果获取接口

2. **用户生成记录管理**
   - 开发用户历史生成记录接口
   - 实现记录删除和隐藏功能

### 阶段四：前端集成（2周）

1. **生成页面优化**
   - 完善风格参数表单
   - 实现生成状态实时更新
   - 优化结果展示界面

2. **我的作品功能**
   - 添加历史生成记录展示
   - 实现图片保存和分享功能

### 阶段五：优化与扩展（1-2周）

1. **性能优化**
   - 优化图片生成速度
   - 添加缓存机制
   - 实现任务队列监控

2. **功能扩展**
   - 添加更多风格选项
   - 实现风格推荐算法
   - 增加用户反馈机制

## 五、技术挑战与解决方案

1. **异构模型接入**
   - 挑战：不同风格可能需要调用不同的API或模型
   - 解决方案：使用适配器模式，为每种API创建标准化接口

2. **资源消耗控制**
   - 挑战：图片生成可能消耗大量服务器资源
   - 解决方案：实现任务限流、队列优先级和资源分配策略

3. **生成质量保障**
   - 挑战：确保生成图片质量和安全性
   - 解决方案：添加内容过滤、质量评估和人工审核机制

## 六、测试与监控计划

1. **单元测试**
   - 测试各风格实现类
   - 验证参数处理逻辑
   - 模拟API响应测试

2. **集成测试**
   - 端到端生成流程测试
   - 任务队列压力测试
   - 数据库事务测试

3. **监控指标**
   - 生成任务成功率
   - 平均生成时间
   - 用户满意度
   - 服务器资源使用情况

此开发计划提供了清晰的架构设计和阶段性目标，您可以根据实际情况调整具体时间和优先级。