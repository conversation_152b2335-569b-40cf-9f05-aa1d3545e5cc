import logging
from datetime import datetime, timedelta
import random
import string
import os
import uuid
import shutil
from pathlib import Path
from typing import Optional

import requests
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from pydantic import BaseModel
from sqlalchemy.orm import Session

from config import Config
from database.connection import db_manager
from database.models import User, UserAuthentication, CoinTransaction, CoinTransactionSource

# ==================== 初始化 ====================
# 日志记录器
logger = logging.getLogger(__name__)

# FastAPI APIRouter实例，用于定义路由
# 这个router实例将被主应用(main.py)导入和注册
router = APIRouter(
    prefix="/wowpic/auth",
    tags=["认证"],
    responses={401: {"description": "身份验证失败"}}
)

# HTTP Bearer Token 安全方案
security = HTTPBearer()


# ==================== Pydantic模型 ====================
class LoginRequest(BaseModel):
    code: str
    platform: str

class LoginResponse(BaseModel):
    token: str
    isNewUser: bool

class ProfileUpdateRequest(BaseModel):
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None

class AvatarUploadResponse(BaseModel):
    file_path: str
    url: str


# ==================== 工具函数 (原 jwt_handler.py) ====================
def create_access_token(data: dict) -> str:
    """为指定用户ID创建JWT Token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=Config.JWT_EXPIRE_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, Config.JWT_SECRET_KEY, algorithm=Config.JWT_ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    """验证JWT Token，成功则返回payload，失败则抛出JWTError"""
    try:
        payload = jwt.decode(token, Config.JWT_SECRET_KEY, algorithms=[Config.JWT_ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"JWT Token验证失败: {e}")
        raise


# ==================== 平台API交互 (原 wx_auth.py) ====================
async def get_wx_openid(code: str):
    """使用微信code换取用户openid和unionid"""
    url = "https://api.weixin.qq.com/sns/jscode2session"
    params = {
        "appid": Config.WX_APPID,
        "secret": Config.WX_SECRET,
        "js_code": code,
        "grant_type": "authorization_code"
    }
    response = requests.get(url, params=params)
    data = response.json()
    if "errcode" in data and data["errcode"] != 0:
        logger.error(f"获取微信openid失败: {data}")
        raise Exception(f"微信授权失败: {data.get('errmsg', '未知错误')}")
    return {"openid": data.get("openid"), "unionid": data.get("unionid")}


# ==================== 业务逻辑 (原 services/auth_service.py) ====================
async def get_or_create_user_by_platform(
    db: Session, platform: str, platform_uid: str, platform_unionid: str = None
):
    """根据平台ID查找或创建用户，实现账户统一管理"""
    # 优先通过unionid查找，实现跨平台账号关联
    if platform_unionid:
        auth_record = db.query(UserAuthentication).filter(
            UserAuthentication.platform_unionid == platform_unionid
        ).first()
        if auth_record:
            # 检查当前平台的认证记录是否存在，不存在则补充
            current_platform_auth = db.query(UserAuthentication).filter(
                UserAuthentication.user_id == auth_record.user_id,
                UserAuthentication.platform == platform,
                UserAuthentication.platform_uid == platform_uid
            ).first()
            if not current_platform_auth:
                new_auth = UserAuthentication(user_id=auth_record.user_id, platform=platform, platform_uid=platform_uid, platform_unionid=platform_unionid)
                db.add(new_auth)
                db.commit()
            return auth_record.user, False

    # 如果没有unionid或unionid未找到，则通过openid查找
    auth_record = db.query(UserAuthentication).filter(
        UserAuthentication.platform == platform,
        UserAuthentication.platform_uid == platform_uid
    ).first()
    if auth_record:
        return auth_record.user, False

    # 如果都找不到，则创建新用户
    # 新用户默认赠送 30 哇图币
    # 生成随机的昵称后缀（2个英文字符+2个数字）
    random_suffix = ''.join(random.choice(string.ascii_letters) for _ in range(2)) + ''.join(random.choice(string.digits) for _ in range(2))
    new_user = User(nickname=f"哇兔兔{random_suffix}", coins=30)
    db.add(new_user)
    db.flush()  # 确保获取到新用户的ID
    
    # 初始赠送 30 哇图币流水
    db.add(
        CoinTransaction(
            user_id=new_user.id,
            change=30,
            balance=new_user.coins,
            source=CoinTransactionSource.MANUAL,
            remark="新用户注册赠送",
        )
    )

    new_auth = UserAuthentication(
        user_id=new_user.id,
        platform=platform,
        platform_uid=platform_uid,
        platform_unionid=platform_unionid
    )
    db.add(new_auth)
    db.commit()
    return new_user, True

async def login_or_register_logic(db: Session, code: str, platform: str):
    """用户登录或注册的核心业务逻辑"""
    if platform == "WX":
        platform_data = await get_wx_openid(code)
        platform_uid = platform_data.get("openid")
        platform_unionid = platform_data.get("unionid")
    else:
        raise Exception(f"不支持的平台类型: {platform}")

    if not platform_uid:
        raise Exception("无法获取平台用户ID")

    user, is_new_user = await get_or_create_user_by_platform(
        db, platform, platform_uid, platform_unionid
    )
    
    user.last_login_at = datetime.utcnow()
    db.commit()

    token = create_access_token({"user_id": user.id})
    
    # 开发环境下打印调试信息
    if Config.DEBUG:
        try:
            token_payload = verify_token(token)
            expire_time = datetime.fromtimestamp(token_payload['exp'])
            logger.info(f"✅ 登录成功: user_id={user.id}, openid={platform_uid}, is_new={is_new_user}")
            logger.info(f"🔑 Token签发: 有效期至 -> {expire_time.strftime('%Y-%m-%d %H:%M:%S')} (UTC)")
        except Exception as e:
            logger.warning(f"无法记录Token调试信息: {e}")
            
    return {"token": token, "isNewUser": is_new_user}

# ==================== 新增方法：头像上传处理 ====================
async def save_avatar(avatar_file: UploadFile, user_id: int) -> str:
    """处理用户头像上传并保存到指定位置"""
    # 确保目录存在
    avatar_dir = Path(Config.STATIC_PATH) / "uploads" / "avatars"
    avatar_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成唯一文件名，使用用户ID加入文件名
    file_extension = os.path.splitext(avatar_file.filename)[1] if avatar_file.filename else ".jpg"
    unique_filename = f"avatar_{user_id}_{uuid.uuid4().hex}{file_extension}"
    file_path = avatar_dir / unique_filename
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(avatar_file.file, buffer)
    
    # 返回与其他图片路径格式一致的路径
    return f"/static/uploads/avatars/{unique_filename}"

# ==================== FastAPI依赖项 (原 dependencies.py) ====================
def get_db():
    """FastAPI依赖项：获取数据库会话"""
    db = next(db_manager.get_db())
    try:
        yield db
    finally:
        db.close()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """FastAPI依赖项：从Token中解析并获取当前用户对象"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的身份验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = verify_token(credentials.credentials)
        user_id = payload.get("user_id")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception
    if user.is_banned:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="该账号已被封禁")
        
    return user


# ==================== API路由 (原 routers/auth.py) ====================
@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest, db: Session = Depends(get_db)):
    """用户登录或自动注册接口"""
    try:
        result = await login_or_register_logic(db, request.code, request.platform)
        return result
    except Exception as e:
        logger.error(f"登录失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"登录失败: {str(e)}"
        )

@router.get("/me", response_model_exclude={"authentications", "generations"})
async def read_users_me(current_user: User = Depends(get_current_user)):
    """获取当前登录用户的信息"""
    return current_user

# ==================== 新增API路由 ====================
@router.post("/avatar", response_model=AvatarUploadResponse)
@router.put("/avatar", response_model=AvatarUploadResponse)
async def upload_avatar(
    avatar: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """上传用户头像接口"""
    try:
        # 验证文件类型
        content_type = avatar.content_type
        if not content_type or not content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="文件必须是图片格式")
        
        # 保存头像
        file_path = await save_avatar(avatar, current_user.id)
        
        # 如果用户有旧头像，可以在此处删除旧文件（可选）
        # 此处简化处理，不删除旧文件
        
        # 更新用户头像路径
        current_user.avatar_url = file_path
        db.commit()
        
        # 返回结果
        return {
            "file_path": file_path,
            "url": f"{Config.BASE_URL}{file_path}"
        }
    except Exception as e:
        logger.error(f"头像上传失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"头像上传失败: {str(e)}")

@router.post("/profile")
@router.put("/profile")
async def update_profile(
    profile: ProfileUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户个人资料"""
    try:
        if profile.nickname is not None and profile.nickname.strip():
            current_user.nickname = profile.nickname.strip()
            
        if profile.avatar_url is not None and profile.avatar_url.strip():
            current_user.avatar_url = profile.avatar_url.strip()
            
        current_user.updated_at = datetime.utcnow()
        db.commit()
        
        return {"success": True, "message": "个人资料更新成功"}
    except Exception as e:
        logger.error(f"更新个人资料失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新个人资料失败: {str(e)}")