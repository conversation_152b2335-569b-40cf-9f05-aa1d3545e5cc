<template>
	<view class="skeleton-container">
		<!-- 安全区域 -->
		<view class="safe-area"></view>
		
		<!-- 头部用户信息区骨架 -->
		<view class="header">
			<view class="user-info">
				<view class="skeleton-avatar"></view>
				<view class="skeleton-text-medium"></view>
			</view>
		</view>
		
		<!-- 风格探索区域骨架 -->
		<view class="section-container">
			<!-- 区块标题骨架 -->
			<view class="fancy-section-header">
				<view class="skeleton-line"></view>
				<view class="skeleton-title"></view>
			</view>
			
			<!-- 横向滚动风格展示骨架 -->
			<view class="style-scroll">
				<view class="style-scroll-content">
					<view class="skeleton-scroll-card" v-for="i in 4" :key="i"></view>
				</view>
			</view>
		</view>
		
		<!-- 活动推荐区域骨架 -->
		<view class="section-container">
			<!-- 区块标题骨架 -->
			<view class="fancy-section-header">
				<view class="skeleton-line"></view>
				<view class="skeleton-title"></view>
			</view>
			
			<!-- Banner骨架 -->
			<view class="skeleton-mini-banner"></view>
		</view>
		
		<!-- 创意灵感区域骨架 -->
		<view class="section-container">
			<!-- 区块标题骨架 -->
			<view class="fancy-section-header">
				<view class="skeleton-line"></view>
				<view class="skeleton-title"></view>
			</view>
			
			<!-- 瀑布流骨架 -->
			<view class="skeleton-waterfall">
				<!-- 左列 -->
				<view class="skeleton-waterfall-column">
					<view class="skeleton-waterfall-item" v-for="i in 2" :key="i">
						<view class="skeleton-waterfall-image"></view>
						<view class="skeleton-waterfall-info">
							<view class="skeleton-text-short"></view>
							<view class="skeleton-text-tiny"></view>
						</view>
					</view>
				</view>
				
				<!-- 右列 -->
				<view class="skeleton-waterfall-column">
					<view class="skeleton-waterfall-item" v-for="i in 2" :key="i">
						<view class="skeleton-waterfall-image"></view>
						<view class="skeleton-waterfall-info">
							<view class="skeleton-text-short"></view>
							<view class="skeleton-text-tiny"></view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '../utils/request.js'

	export default {
		name: 'SkeletonLoading',
		props: {
			// 是否自动加载用户信息
			autoLoadUser: {
				type: Boolean,
				default: true
			},
			// 最小显示时间（毫秒）
			minDisplayTime: {
				type: Number,
				default: 1500
			}
		},
		data() {
			return {
				startTime: 0,
				userLoadCompleted: false,
				userLoadSuccess: false,
				userInfo: null,
				errorMessage: ''
			}
		},
		async mounted() {
			this.startTime = Date.now();

			if (this.autoLoadUser) {
				await this.loadUserInfo();
			}
		},
		methods: {
			async loadUserInfo() {
				try {
					const app = getApp();

					// 等待全局登录完成
					await app.globalData.loginPromise;

					// 加载用户信息
					const res = await request.get('/wowpic/auth/me', null, { showLoading: false });
					this.userInfo = res;
					app.globalData.userInfo = res;
					this.userLoadSuccess = true;

					console.log('骨架屏：用户信息加载成功', res);

				} catch (error) {
					console.error('骨架屏：用户信息加载失败', error);
					this.userLoadSuccess = false;
					this.errorMessage = '用户信息加载失败';
				} finally {
					this.userLoadCompleted = true;
					this.checkAndEmitComplete();
				}
			},

			async checkAndEmitComplete() {
				// 确保最小显示时间
				const elapsedTime = Date.now() - this.startTime;
				const remainingTime = Math.max(0, this.minDisplayTime - elapsedTime);

				if (remainingTime > 0) {
					await new Promise(resolve => setTimeout(resolve, remainingTime));
				}

				// 发送加载完成事件
				this.$emit('load-complete', {
					success: this.userLoadSuccess,
					userInfo: this.userInfo,
					error: this.errorMessage
				});
			}
		}
	}
</script>

<style>
	/* 骨架屏基础样式 */
	.skeleton-container {
		padding: 0 24rpx;
		height: 100vh;
		background-color: #F8F8F8;
	}
	
	@keyframes shimmer {
		0% {
			background-position: -400px 0;
		}
		100% {
			background-position: 400px 0;
		}
	}
	
	.skeleton-text-tiny,
	.skeleton-text-short, 
	.skeleton-text-medium, 
	.skeleton-avatar,
	.skeleton-mini-banner,
	.skeleton-scroll-card,
	.skeleton-waterfall-image,
	.skeleton-line,
	.skeleton-title {
		background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
		background-size: 800px 100%;
		animation: shimmer 2s infinite linear;
		border-radius: 8rpx;
	}
	
	/* 安全区域 */
	.safe-area {
		height: 88rpx;
	}
	
	/* 头部样式 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
		padding: 0 6rpx;
	}
	
	.user-info {
		display: flex;
		align-items: center;
	}
	
	.skeleton-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 16rpx;
	}
	
	.skeleton-text-medium {
		width: 150rpx;
		height: 32rpx;
	}
	
	/* 区块容器 */
	.section-container {
		margin-bottom: 20rpx;
		position: relative;
	}
	
	/* 精美的区块标题骨架 */
	.fancy-section-header {
		margin-bottom: 24rpx;
		display: flex;
		flex-direction: column;
		position: relative;
		padding-left: 20rpx;
	}
	
	.skeleton-line {
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		border-radius: 4rpx;
	}
	
	.skeleton-title {
		width: 120rpx;
		height: 36rpx;
	}
	
	/* 横向滚动风格卡片骨架 */
	.style-scroll {
		white-space: nowrap;
		margin: 0 -24rpx 0 -24rpx;
		padding: 0 24rpx;
	}
	
	.style-scroll-content {
		display: inline-flex;
		padding: 10rpx 0;
	}
	
	.skeleton-scroll-card {
		width: 280rpx;
		height: 380rpx;
		margin-right: 20rpx;
		border-radius: 16rpx;
	}
	
	/* Banner骨架 */
	.skeleton-mini-banner {
		width: 100%;
		height: 160rpx;
		border-radius: 16rpx;
		margin-bottom: 0;
	}
	
	/* 瀑布流骨架 */
	.skeleton-waterfall {
		display: flex;
		justify-content: space-between;
		margin-bottom: 0;
	}
	
	.skeleton-waterfall-column {
		width: 48%;
	}
	
	.skeleton-waterfall-item {
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		background-color: #FFFFFF;
	}
	
	.skeleton-waterfall-image {
		width: 100%;
		height: 240rpx;
		display: block;
		border-radius: 0;
	}
	
	.skeleton-waterfall-info {
		padding: 16rpx;
		background: #FFFFFF;
	}
	
	.skeleton-text-short {
		width: 80%;
		height: 28rpx;
		margin-bottom: 8rpx;
	}
	
	.skeleton-text-tiny {
		width: 50%;
		height: 22rpx;
	}
</style> 