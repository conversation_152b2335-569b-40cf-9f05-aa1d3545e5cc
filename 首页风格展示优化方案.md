# 首页风格展示优化方案

## 问题背景

当前WowPic小程序首页存在两个主要问题：

1. **首屏重复风格展示**：在"风格探索"(顶部横向滚动)和"创意灵感"(底部瀑布流)区域显示了相同的风格卡片，导致用户体验重复冗余。

2. **多图风格展示受限**：当一个风格有多张示例图片时，目前只能显示一张封面图，无法充分展示风格的多样性。

## 优化方案

### 1. 区分风格展示区域内容

将两个区域的内容进行差异化展示：

- **风格探索区域**：展示热门/推荐风格（数据库中`is_popular=true`的风格）
- **创意灵感区域**：展示其他风格或更多创意内容（排除已在热门区域展示的风格）

### 2. 多图风格轮播展示

对于拥有多张示例图片的风格，在瀑布流区域实现轮播图展示功能。

## 实施步骤

### 后端修改

1. **数据库调整**：
   ```python
   class Style(Base):
       # 现有字段...
       
       # 新增字段
       example_images = Column(JSON, nullable=True, comment='风格示例图片URLs数组')
   ```

2. **接口调整**：
   - 修改`/wowpic/styles/popular`接口，确保返回热门风格
   - 修改`/wowpic/styles`接口，返回所有风格，包含`example_images`字段

### 前端修改

1. **风格加载逻辑优化**：
   ```javascript
   // 加载热门风格和全部风格
   async loadStyles() {
     try {
       // 分别加载热门风格和全部风格
       const [popularStyles, allStyles] = await Promise.all([
         request.get('/wowpic/styles/popular', null, { showLoading: false }),
         request.get('/wowpic/styles', null, { showLoading: false })
       ]);
       
       // 处理顶部滚动区域(热门风格) - 仅使用cover_image_url
       this.aiTools = popularStyles.map(style => ({
         id: style.id,
         name: style.name,
         desc: style.description,
         image: style.cover_image_url || '/static/logo.png'
       }));
       
       // 处理瀑布流区域(全部风格中排除已在热门中显示的)
       const popularIds = new Set(popularStyles.map(style => style.id));
       const nonPopularStyles = allStyles.filter(style => !popularIds.has(style.id));
       
       // 更新瀑布流数据
       this.initWaterfall(nonPopularStyles);
     } catch (err) {
       console.error('获取风格列表失败', err);
       throw err;
     }
   }
   
   // 初始化瀑布流数据
   initWaterfall(styles) {
     this.waterfall = {
       left: [],
       right: []
     };
     
     if (styles && styles.length > 0) {
       // 分配到左右两列
       styles.forEach((style, index) => {
         const item = {
           id: style.id,
           name: style.name,
           desc: style.description,
           image: style.cover_image_url || '/static/logo.png',
           // 如果example_images存在且有内容，则使用它，否则使用null
           sliderImages: (style.example_images && style.example_images.length > 0) ? style.example_images : null
         };
         
         // 偶数放左边，奇数放右边
         if (index % 2 === 0) {
           this.waterfall.left.push(item);
         } else {
           this.waterfall.right.push(item);
         }
       });
     }
   }
   ```

2. **瀑布流轮播图实现**：
   ```html
   <!-- 瀑布流区域项目实现轮播图 -->
   <view class="waterfall-item" v-for="(item, index) in waterfall.left" :key="index" 
     @click="navigateToGenerate(item)">
     <!-- 如果有多张图片，则使用轮播图 -->
     <swiper v-if="item.sliderImages && item.sliderImages.length > 1" 
       class="waterfall-swiper"
       :circular="true" 
       :autoplay="true" 
       :interval="3000" 
       :duration="500"
       :indicator-dots="true"
       indicator-color="rgba(255,255,255,0.4)"
       indicator-active-color="#FFFFFF">
       <swiper-item v-for="(img, imgIndex) in item.sliderImages" :key="imgIndex">
         <image class="waterfall-image" :src="getImageUrl(img)" mode="widthFix"></image>
       </swiper-item>
     </swiper>
     <!-- 单图显示 -->
     <image v-else class="waterfall-image" :src="getImageUrl(item.image)" mode="widthFix"></image>
     
     <view class="waterfall-content">
       <text class="waterfall-desc">{{item.desc}}</text>
       <text class="waterfall-name">{{item.name}}</text>
     </view>
   </view>
   
   <!-- 右列也需要同样的修改 -->
   <view class="waterfall-column">
     <view class="waterfall-item" v-for="(item, index) in waterfall.right" :key="index" 
       @click="navigateToGenerate(item)">
       <!-- 如果有多张图片，则使用轮播图 -->
       <swiper v-if="item.sliderImages && item.sliderImages.length > 1" 
         class="waterfall-swiper"
         :circular="true" 
         :autoplay="true" 
         :interval="3000" 
         :duration="500"
         :indicator-dots="true"
         indicator-color="rgba(255,255,255,0.4)"
         indicator-active-color="#FFFFFF">
         <swiper-item v-for="(img, imgIndex) in item.sliderImages" :key="imgIndex">
           <image class="waterfall-image" :src="getImageUrl(img)" mode="widthFix"></image>
         </swiper-item>
       </swiper>
       <!-- 单图显示 -->
       <image v-else class="waterfall-image" :src="getImageUrl(item.image)" mode="widthFix"></image>
       
       <view class="waterfall-content">
         <text class="waterfall-desc">{{item.desc}}</text>
         <text class="waterfall-name">{{item.name}}</text>
       </view>
     </view>
   </view>
   ```

3. **添加轮播图样式**：
   ```css
   .waterfall-swiper {
     width: 100%;
     height: auto;
     aspect-ratio: 0.75; /* 保持图片比例，可根据实际需求调整 */
   }
   
   .waterfall-swiper .uni-swiper-dot {
     width: 8rpx;
     height: 8rpx;
     border-radius: 4rpx;
     margin: 0 4rpx;
   }
   ```

## 测试计划

1. **后端测试**：
   - 验证数据库字段添加是否成功
   - 测试接口返回的数据格式是否正确，特别是`example_images`字段

2. **前端测试**：
   - 测试风格探索区域是否只显示热门风格，且使用单张封面图
   - 测试创意灵感区域是否显示非重复风格
   - 测试轮播图功能在有多张图片时是否正常工作
   - 测试在不同设备尺寸下的显示效果

## 预期效果

1. **首页内容更丰富**：通过区分两个区域的内容，用户可以一次性看到更多不同的风格选项
2. **风格展示更全面**：通过轮播图展示，用户可以更全面地了解每种风格的效果
3. **用户体验更流畅**：避免重复内容，提高页面信息密度和有效性

## 进度安排

1. **数据库修改**：1天
2. **后端接口调整**：1天
3. **前端实现**：2天
4. **测试与优化**：1天

总计开发时间：5个工作日 