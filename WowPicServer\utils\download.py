import os
import uuid
import aiohttp
import aiofiles
import logging
import asyncio
from pathlib import Path
from urllib.parse import urlparse
from typing import List, Dict
from config import Config

logger = logging.getLogger(__name__)

async def download_image(url: str, user_id: int) -> str:
    """
    下载远程图片到本地存储
    
    Args:
        url: 远程图片URL
        user_id: 用户ID，用于创建用户专属目录
        
    Returns:
        str: 本地存储的图片URL路径
    """
    # 如果URL已经是本地路径，直接返回
    if url.startswith(Config.BASE_URL) or url.startswith("/static/"):
        # 如果是完整URL，提取相对路径部分
        if url.startswith("http"):
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.path
        return url

    # 处理临时文件（来自tuzi_provider的base64图片）
    if url.startswith("file://"):
        temp_file_path = url[7:]  # 移除 "file://" 前缀

        # 创建存储目录
        generated_dir = Path(Config.UPLOAD_DIR).parent / "generated" / f"user_{user_id}"
        os.makedirs(generated_dir, exist_ok=True)

        # 生成唯一文件名
        filename = f"{uuid.uuid4()}.png"
        file_path = generated_dir / filename

        try:
            # 复制临时文件到目标位置
            import shutil
            shutil.copy2(temp_file_path, file_path)

            # 删除临时文件
            os.unlink(temp_file_path)

            # 返回相对路径
            return f"/static/generated/user_{user_id}/{filename}"
        except Exception as e:
            logger.error(f"处理临时文件失败: {e}")
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except:
                pass
            raise
        
    # 创建存储目录
    generated_dir = Path(Config.UPLOAD_DIR).parent / "generated" / f"user_{user_id}"
    os.makedirs(generated_dir, exist_ok=True)
    
    # 生成唯一文件名
    filename = f"{uuid.uuid4()}.png"
    file_path = generated_dir / filename
    
    # 设置最大重试次数（适当提高，降低偶发网络错误影响）
    max_retries = 5
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # 下载图片
            async with aiohttp.ClientSession() as session:
                # 对于 filesystem.site 域名，调整为 /cdn/download/ 路径并添加浏览器 UA 与 Referer
                headers = {}
                if "filesystem.site" in url:
                    # 若链接形如 https://filesystem.site/cdn/2025xxx/xxx.png -> 改为 .../cdn/download/2025xxx/xxx.png
                    if "/cdn/" in url and "/cdn/download/" not in url:
                        download_url_candidate = url.replace("/cdn/", "/cdn/download/", 1)
                        url = download_url_candidate

                    headers = {
                        "User-Agent": (
                            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                        ),
                        "Referer": "https://aihubmix.com/",
                    }

                # 将整体下载超时时间从 30 秒提升至 120 秒，避免大文件或网络慢时超时
                timeout = aiohttp.ClientTimeout(total=120, sock_connect=30, sock_read=90)
                # 云服务器环境可能缺少部分根证书，导致验证 https 证书失败。
                # 对于公开图片资源（尤其是第三方 CDN）问题不大，因此在此关闭 SSL 校验，避免下载异常。
                async with session.get(url, headers=headers, timeout=timeout, ssl=False) as response:
                    if response.status != 200:
                        logger.error(f"下载图片失败，状态码: {response.status}, URL: {url}")
                        retry_count += 1
                        if retry_count < max_retries:
                            await asyncio.sleep(2)
                            continue
                        # 进入统一失败流程，稍后尝试回退方案
                        pass

                    # 保存文件
                    async with aiofiles.open(file_path, "wb") as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)

            # 构建新的相对路径 /static/generated/user_xx/xxx.png
            relative_path = Path("static") / "generated" / f"user_{user_id}" / filename
            local_url = f"/{relative_path.as_posix()}"  # 确保以 / 开头

            # 成功时不输出详细日志，只在失败时输出
            return local_url

        except asyncio.TimeoutError:
            logger.error(f"下载图片超时: {url}")
            retry_count += 1
            if retry_count < max_retries:
                await asyncio.sleep(2)
            else:
                logger.error("下载图片超时，已达到最大重试次数")
                # 失败后继续进入回退流程
                pass

        except Exception as e:
            logger.exception(f"下载图片异常: {e}, URL: {url}")
            retry_count += 1
            if retry_count < max_retries:
                await asyncio.sleep(2)
            else:
                pass  # 失败后进入回退流程

        # ------------------------------
        # 回退策略
        # ------------------------------
        # 若是 filesystem.site 且使用 /cdn/download/ 路径，尝试回退到 /cdn/ 直链
        if "filesystem.site" in url and "/cdn/download/" in url:
            fallback_url = url.replace("/cdn/download/", "/cdn/", 1)
            logger.warning(f"下载失败，回退尝试原始 CDN 链接: {fallback_url}")
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(fallback_url, timeout=timeout, ssl=False) as resp:
                        if resp.status == 200:
                            async with aiofiles.open(file_path, "wb") as f:
                                async for chunk in resp.content.iter_chunked(8192):
                                    await f.write(chunk)

                    # 保存成功后返回本地路径
                    relative_path = Path("static") / "generated" / f"user_{user_id}" / filename
                    local_url = f"/{relative_path.as_posix()}"
                    logger.info(f"回退下载成功: {fallback_url}")
                    return local_url
            except Exception as e:
                logger.error(f"回退下载仍失败: {e}")

        # 最终回退：直接返回远程 URL，供前端直链
        logger.error("下载仍失败，返回远程 URL 供前端直接访问")
        return url


async def download_multiple_images(urls: List[str], user_id: int) -> List[str]:
    """
    并行下载多张图片并返回本地路径列表
    
    Args:
        urls: 远程图片URL列表
        user_id: 用户ID
        
    Returns:
        List[str]: 本地图片URL路径列表
    """
    if not urls:
        return []
        
    # 使用信号量限制并发下载数量，避免与部分 CDN/防盗链策略冲突
    # 这里将并发数限制为 1（串行下载），如果后续验证无问题，可适当调大（例如 2~3）
    semaphore = asyncio.Semaphore(1)

    async def _safe_download(u: str):
        """在受限并发环境下下载单张图片"""
        async with semaphore:
            return await download_image(u, user_id)

    download_tasks = [_safe_download(u) for u in urls]

    # 等待所有下载任务完成并收集结果
    results = await asyncio.gather(*download_tasks, return_exceptions=True)

    local_urls: List[str] = []
    for i, result in enumerate(results):
        if isinstance(result, str):
            local_urls.append(result)  # 允许本地或远程 URL
        elif result is None:
            logger.error(f"下载图片失败，未获取到有效数据: {urls[i]}")
        else:
            logger.error(f"下载图片 {urls[i]} 失败: {str(result)}")

    return local_urls 