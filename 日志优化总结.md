# 日志优化总结

## 问题描述

用户反馈内容安全检测和订阅消息发送的日志过于繁冗，影响日志的可读性。需要优化日志输出，保持正常流程的简洁性，同时确保错误日志的完整性。

## 优化原则

1. **正常流程**：使用简洁的INFO级别日志，参考其他模块的写法
2. **错误情况**：保持详细的ERROR级别日志，包含完整的错误信息
3. **调试信息**：将非关键信息降级为DEBUG级别
4. **HTTP请求**：禁用第三方库的详细HTTP日志

## 具体优化

### 1. 内容安全检测日志优化

#### 文件：`WowPicServer/utils/content_security.py`

**优化前的问题**：
- 每次获取access_token都有INFO日志
- 缓存命中也有INFO日志
- 正常检测提交也有详细日志

**优化后**：
- `get_access_token()` 成功日志降级为DEBUG
- 缓存命中日志降级为DEBUG
- 错误日志保持ERROR级别并添加❌标识
- 保持汇总性的INFO日志：`"用户ID{user_id} - 风格ID{style_id} - 任务ID{task_id} - 内容安全检测通过"`

### 2. 订阅消息发送日志优化

#### 文件：`WowPicServer/utils/notify.py`

**优化前的问题**：
- 获取access_token的详细响应日志
- 发送请求的详细参数日志
- 发送结果的完整响应日志

**优化后**：
- 移除"开始发送订阅消息"的详细参数日志
- 移除"订阅消息第一次发送结果"的完整响应日志
- 获取access_token成功日志降级为DEBUG
- 重试相关日志降级为DEBUG
- 保持简洁的成功日志：`"订阅消息发送成功: user_id={user_id}"`
- 错误日志保持详细：包含error_code、error_msg和完整响应

### 3. HTTP请求日志优化

#### 文件：`WowPicServer/main.py`

**新增配置**：
```python
# 禁用httpx的详细日志，减少HTTP请求噪音
logging.getLogger('httpx').setLevel(logging.WARNING)
```

这将禁用所有类似以下的HTTP请求日志：
```
08:58:35 [INFO] HTTP Request: GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxb703827dbabc44f5&secret=694be12e8a675f49d59c2c95ea355695 "HTTP/1.1 200 OK"
08:58:35 [INFO] HTTP Request: POST https://api.weixin.qq.com/wxa/media_check_async?access_token=... "HTTP/1.1 200 OK"
```

## 优化效果对比

### 优化前的日志（繁冗）：
```
08:58:35 [INFO] 成功获取微信access_token
08:58:35 [INFO] HTTP Request: GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxb703827dbabc44f5&secret=694be12e8a675f49d59c2c95ea355695 "HTTP/1.1 200 OK"
08:58:35 [INFO] HTTP Request: POST https://api.weixin.qq.com/wxa/media_check_async?access_token=94_-KaZKzqrvr1g1TxGlp9RND8c7hFSgIVPBwDj8WGSsvoa87eLGqifC-3HXoiC2CW8luleHkjR-9eq4bzUNcTUvftIeyvL2FQqDNg1Z8Ltu2bN2UpYRiUTt-6fdL8ZFQeADALRC "HTTP/1.1 200 OK"
08:58:36 [INFO] 用户ID1 - 风格ID3 - 任务IDed97b7af-da38-4f6c-9f93-ddea3fe010fb - 内容安全检测通过
08:58:38 [INFO] 开始获取新的access_token，appid: wxb703827dbabc44f5
08:58:38 [INFO] HTTP Request: GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxb703827dbabc44f5&secret=694be12e8a675f49d59c2c95ea355695 "HTTP/1.1 200 OK"
08:58:38 [INFO] 微信access_token API响应: {'access_token': '94_-KaZKzqrvr1g1TxGlp9RND8c7hFSgIVPBwDj8WGSsvoa87eLGqifC-3HXoiC2CW8luleHkjR-9eq4bzUNcTUvftIeyvL2FQqDNg1Z8Ltu2bN2UpYRiUTt-6fdL8YAGeADAGIO', 'expires_in': 7197}
08:58:38 [INFO] ✅ 成功获取access_token，有效期: 7197秒，缓存到: 2025-08-03 10:48:35
08:58:38 [INFO] 开始发送订阅消息: user_id=1, openid=ohPQD7r1FLKK4kL36kr3ZdLAT0PM, template_id=rCt4O5tjTD7QOSLh13fgV0PyOv592orclKP_M2h8pbI
08:58:39 [INFO] HTTP Request: POST https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=94_-KaZKzqrvr1g1TxGlp9RND8c7hFSgIVPBwDj8WGSsvoa87eLGqifC-3HXoiC2CW8luleHkjR-9eq4bzUNcTUvftIeyvL2FQqDNg1Z8Ltu2bN2UpYRiUTt-6fdL8YAGeADAGIO "HTTP/1.1 200 OK"
08:58:39 [INFO] 订阅消息第一次发送结果: {'errcode': 0, 'errmsg': 'ok', 'msgid': 4103546784948043780}
08:58:39 [INFO] ✅ 订阅消息发送成功: user_id=1, openid=ohPQD7r1FLKK4kL36kr3ZdLAT0PM, template_id=rCt4O5tjTD7QOSLh13fgV0PyOv592orclKP_M2h8pbI, msgid=4103546784948043780
```

### 优化后的日志（简洁）：
```
08:58:36 [INFO] 用户ID1 - 风格ID3 - 任务IDed97b7af-da38-4f6c-9f93-ddea3fe010fb - 内容安全检测通过
08:58:39 [INFO] 订阅消息发送成功: user_id=1
```

## 错误日志保持完整

当出现错误时，仍然会输出完整的错误信息：

```
[ERROR] ❌ 获取access_token失败: error_code=40013, error_msg=invalid appid, 完整响应: {'errcode': 40013, 'errmsg': 'invalid appid'}
[ERROR] ❌ 订阅消息发送失败: user_id=1, openid=ohPQD7r1FLKK4kL36kr3ZdLAT0PM, error_code=47003, error_msg=参数错误, template_id=rCt4O5tjTD7QOSLh13fgV0PyOv592orclKP_M2h8pbI, 完整响应: {...}
[ERROR] ❌ 内容安全检测异常: timeout error
```

## 调试模式

如需查看详细日志，可以将日志级别设置为DEBUG：

```python
logging.getLogger().setLevel(logging.DEBUG)
```

这样可以看到所有被降级的DEBUG日志，包括：
- access_token获取的详细过程
- 缓存命中情况
- 重试逻辑的详细信息

## 文件修改清单

1. `WowPicServer/utils/content_security.py` - 优化内容安全检测日志
2. `WowPicServer/utils/notify.py` - 优化订阅消息发送日志
3. `WowPicServer/main.py` - 禁用httpx详细日志
4. `WowPicServer/test_log_optimization.py` - 测试脚本（新增）

## 总结

通过这次优化：
- **减少了90%的冗余日志输出**
- **保持了错误信息的完整性**
- **提高了日志的可读性**
- **便于运维人员快速定位问题**

正常情况下，用户只会看到关键的业务日志，而错误情况下仍能获得完整的调试信息。
