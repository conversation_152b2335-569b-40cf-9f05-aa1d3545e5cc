# 新的风格管理架构 - 使用厂商模式
# 风格实现现在通过StyleFactory动态加载，不再需要在这里导入具体的风格类

from .base_style import BaseStyle
from .universal_style import UniversalStyle
from .style_config import (
    STYLE_PROVIDER_MAPPING,
    get_provider_for_style,
    get_all_supported_styles,
    is_style_supported
)

# 导入厂商实现
from services.providers import BaseProvider, AihubmixProvider, TuziProvider

__all__ = [
    'BaseStyle',
    'UniversalStyle',
    'BaseProvider',
    'AihubmixProvider',
    'TuziProvider',
    'STYLE_PROVIDER_MAPPING',
    'get_provider_for_style',
    'get_all_supported_styles',
    'is_style_supported'
]