import asyncio
import base64
import io
import logging
import os
import re
from typing import List, Any

import httpx
from openai import OpenAI

from config import Config
from .base_provider import BaseProvider

logger = logging.getLogger(__name__)

class TuziChatProvider(BaseProvider):
    """兔子API厂商实现 - Chat模式（使用gpt-4o-image-vip模型）"""

    @property
    def provider_name(self) -> str:
        return "tuzi_chat"

    @property
    def supported_features(self) -> dict:
        return {
            "supports_streaming": True,
            "supports_multi_image": True,
            # 移除supports_n_param，不再支持n参数
            "max_images_per_request": 4,
            "max_source_images": 10,
            "requires_source_images": False
        }

    def validate_params(self, params: dict) -> bool:
        """验证参数有效性"""
        return True  # 基本验证，可以根据需要扩展

    def __init__(self):
        # 允许通过环境变量 TUZI_API_KEY 覆盖，默认回退到 Config.AIHUBMIX_API_KEY 以兼容旧配置
        api_key = os.getenv("TUZI_API_KEY", Config.AIHUBMIX_API_KEY)
        if not api_key:
            logger.error("TUZI_API_KEY 未配置，无法使用兔子API厂商")
            raise RuntimeError("TUZI_API_KEY 未配置，无法使用兔子API厂商")

        # API Base 与超时时间
        api_base = os.getenv("TUZI_API_BASE", "https://api.tu-zi.com/v1")
        request_timeout = int(os.getenv("TUZI_TIMEOUT", "6000"))

        self.client = OpenAI(
            api_key=api_key,
            base_url=api_base,
            http_client=httpx.Client(
                timeout=httpx.Timeout(request_timeout, connect=60),
                verify=False  # 兔子证书偶发异常，关闭更稳
            )
        )

        # 关闭 SSL warning
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    async def generate_image(self, prompt: str, source_images: List[str]) -> List[str]:
        """调用兔子 gpt-4o-image-vip 接口生成图片"""
        try:
            # 直接使用完整提示词，不再重复处理ratio参数
            # API层已经构建了包含ratio的完整提示词
            base_prompt = prompt

            # 只在出错时才输出详细日志，成功时保持简洁

            # 生成图片并获取URL列表，不传递n参数
            return await self._generate_with_chat(base_prompt, source_images)

        except Exception as e:
            logger.error(f"TuziChatProvider 生成图片失败: {str(e)}")
            raise RuntimeError(f"图片生成失败: {str(e)}")

    async def _generate_with_chat(self, prompt: str, image_urls: List[str]) -> List[str]:
        """使用 chat.completions 接口生成图片"""
        try:
            # 构建聊天内容
            chat_content = [{"type": "text", "text": prompt}]
            
            # 如果有图片URL，添加图片内容
            if image_urls and len(image_urls) > 0:
                for image_url in image_urls:
                    try:
                        # 获取图片并转换为base64
                        base64_image = await self._get_image_as_base64(image_url)

                        # 图片MIME类型
                        img_mime = "image/jpeg"  # 默认JPEG
                        if image_url.lower().endswith((".png", ".webp")):
                            img_mime = "image/png"

                        # 添加图片到内容中
                        chat_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{img_mime};base64,{base64_image}"
                            }
                        })
                        # 成功时不输出详细日志
                    except Exception as e:
                        logger.error(f"处理图片 {image_url} 时出错: {str(e)}")
                        # 图片处理失败应该抛出异常，不能跳过用户上传的图片
                        raise RuntimeError(f"处理用户上传的图片失败: {str(e)}")
            
            # 异步包装同步API调用（使用流式，提高超时容忍度）
            def _call_chat_api():
                try:
                    
                    stream_iter = self.client.chat.completions.create(
                        model="gpt-4o-image-vip",
                        messages=[
                            {
                                "role": "user",
                                "content": chat_content
                            }
                        ],
                        stream=True  # 使用流式，避免一次性长时间阻塞
                    )

                    buffer: str = ""
                    image_urls: List[str] = []  # 存储所有找到的图片URL
                    
                    # 辅助函数：URL归一化处理
                    def _normalize_url(u: str) -> str:
                        # 清理URL格式
                        u = u.strip().rstrip(')')

                        # 对 filesystem.site 地址做归一化
                        if "filesystem.site" in u and "/cdn/download/" in u:
                            u = u.replace("/cdn/download/", "/cdn/")

                        # 移除URL中的查询参数（保留主要路径）
                        if '?' in u:
                            u = u.split('?')[0]

                        # 移除fragment
                        if '#' in u:
                            u = u.split('#')[0]

                        return u

                    # 记录已收集的归一化URL，避免同图重复
                    canonical_set: set[str] = set()

                    for chunk in stream_iter:
                        if chunk.choices and len(chunk.choices) > 0:
                            delta = chunk.choices[0].delta
                            if delta and delta.content:
                                buffer += delta.content

                                # 实时提取图片URL，支持多种域名和格式
                                # 按优先级顺序匹配不同的URL模式
                                url_patterns = [
                                    # 优先匹配已知的图片服务域名
                                    r'https://filesystem\.site/[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)',
                                    r'https://[a-zA-Z0-9.-]*cdn[a-zA-Z0-9.-]*\.[a-zA-Z]{2,}/[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)',
                                    r'https://[a-zA-Z0-9.-]*img[a-zA-Z0-9.-]*\.[a-zA-Z]{2,}/[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)',
                                    r'https://[a-zA-Z0-9.-]*static[a-zA-Z0-9.-]*\.[a-zA-Z]{2,}/[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)',
                                    # 通用HTTPS图片URL模式
                                    r'https://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)',
                                    # 最宽泛的匹配
                                    r'https://[^\s\)]+\.(?:png|jpg|jpeg|gif|webp)'
                                ]

                                # 需要排除的域名列表（这些域名通常不提供图片服务）
                                excluded_domains = [
                                    'videos.openai.com',
                                    'video.openai.com',
                                    'api.openai.com',
                                    'platform.openai.com'
                                ]

                                for pattern in url_patterns:
                                    matches = re.findall(pattern, buffer, re.IGNORECASE)
                                    for match in matches:
                                        # 检查是否为排除的域名
                                        is_excluded = any(domain in match.lower() for domain in excluded_domains)
                                        if not is_excluded:
                                            canonical_url = _normalize_url(match)
                                            if canonical_url not in canonical_set:
                                                canonical_set.add(canonical_url)
                                                image_urls.append(canonical_url)

                    # 如果没有找到图片URL，记录完整响应内容用于调试
                    if len(image_urls) == 0:
                        logger.warning(f"TuziChat API响应中未找到图片URL，响应内容: {buffer[:500]}...")

                    # 只输出汇总日志
                    logger.info(f"TuziChat生成完成，收集到 {len(image_urls)} 张图片")
                    return image_urls

                except Exception as e:
                    logger.error(f"TuziChat API 调用失败: {e}")
                    raise

            result = await asyncio.to_thread(_call_chat_api)

            if not result or len(result) == 0:
                raise RuntimeError("API 未返回任何图片链接")

            return result

        except Exception as e:
            logger.error(f"TuziChatProvider _generate_with_chat 失败: {str(e)}")
            raise

    async def _get_image_as_base64(self, image_url: str) -> str:
        """获取图片并转换为base64编码"""
        try:
            # 处理相对路径，转换为完整URL
            if not image_url.startswith("http"):
                # 确保路径以 / 开头
                if not image_url.startswith("/"):
                    image_url = f"/{image_url}"
                # 使用配置的BASE_URL
                from config import Config
                full_url = f"{Config.BASE_URL}{image_url}"
            else:
                full_url = image_url

            # 成功时不输出详细日志

            async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                response = await client.get(full_url)
                response.raise_for_status()

                # 将图片内容转换为base64
                image_data = response.content
                base64_image = base64.b64encode(image_data).decode('utf-8')

                return base64_image
        except Exception as e:
            logger.error(f"获取图片 {image_url} 失败: {str(e)}")
            # 图片获取失败应该抛出异常，而不是跳过
            raise RuntimeError(f"无法获取用户上传的图片: {image_url}，请检查图片是否存在或联系客服")
