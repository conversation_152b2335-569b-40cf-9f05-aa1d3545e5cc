/**
 * 图片处理相关的混入
 * 包含图片选择、删除、上传等通用方法
 */
import request from './request.js'
import { baseUrl } from './config.js'

export default {
	data() {
		return {
			tempImages: [],  // 本地临时图片路径数组
			uploadedImageUrls: [], // 上传到服务器后的图片URL数组
			isUploading: false, // 是否正在上传图片
			maxImagesCount: 4, // 最大可上传图片数量
		}
	},
	
	methods: {
		// 加载用户信息
		loadUserInfo() {
			const app = getApp();
			if (app.globalData.userInfo) {
				this.userCoins = app.globalData.userInfo.coins || 0;
				return;
			}
			
			request.get('/wowpic/auth/me').then(res => {
				if (res) {
					this.userCoins = res.coins || 0;
					app.globalData.userInfo = res;
				}
			}).catch(err => {
				console.error('获取用户信息失败', err);
			});
		},

		// 选择图片
		chooseImage() {
			// 检查是否达到最大上传数量
			if(this.tempImages.length >= this.maxImagesCount) {
				uni.showToast({
					title: `最多上传${this.maxImagesCount}张图片`,
					icon: 'none'
				});
				return;
			}
			
			uni.chooseImage({
				count: this.maxImagesCount - this.tempImages.length, // 可选择的剩余数量
				success: (res) => {
					// 将选择的图片添加到数组中
					this.tempImages = [...this.tempImages, ...res.tempFilePaths];
					// 触发图片变化回调（如果存在）
					if (this.onImagesChanged) {
						this.onImagesChanged();
					}
				}
			});
		},
		
		// 删除选定的图片
		deleteImage(index) {
			// 删除临时图片
			this.tempImages.splice(index, 1);
			// 如果已经有上传的URL，也要删除
			if(this.uploadedImageUrls.length > index) {
				this.uploadedImageUrls.splice(index, 1);
			}
			// 触发图片变化回调（如果存在）
			if (this.onImagesChanged) {
				this.onImagesChanged();
			}
		},
		
		// 上传图片到服务器 - 处理多张图片
		async uploadImage() {
			// 如果没有图片需要上传，则返回空数组
			if(this.tempImages.length === 0 || this.isUploading) {
				return this.uploadedImageUrls;
			}
			
			this.isUploading = true;
			
			// 存储所有上传后的URL
			let uploadedUrls = [...this.uploadedImageUrls];
			
			// 判断是否已经是服务器上的静态资源的函数
			const isServerPath = (url) => {
				if (!url) return false;
				return url.startsWith('/static') || url.includes(`${baseUrl}/static`);
			};
			
			// 创建一个队列，逐个处理每张图片
			for(let i = 0; i < this.tempImages.length; i++) {
				// 如果该位置已经有上传的URL，且是有效的服务器路径，则跳过
				if(i < uploadedUrls.length && isServerPath(uploadedUrls[i])) {
					continue;
				}
				
				// 如果当前临时图片已经是服务器路径
				if(isServerPath(this.tempImages[i])) {
					const serverPath = this.tempImages[i].replace(baseUrl, '');
					// 更新或添加到上传URL数组
					if(i < uploadedUrls.length) {
						uploadedUrls[i] = serverPath;
					} else {
						uploadedUrls.push(serverPath);
					}
					continue;
				}
				
				// 需要上传的情况
				try {
					// 使用Promise封装上传过程
					const uploadedUrl = await new Promise((resolve, reject) => {
						uni.uploadFile({
							url: baseUrl + '/wowpic/upload/image',
							filePath: this.tempImages[i],
							name: 'file',
							header: {
								'Authorization': 'Bearer ' + uni.getStorageSync('token')
							},
							success: (uploadRes) => {
								let result;
								try {
									// 解析响应数据
									if(typeof uploadRes.data === 'string') {
										result = JSON.parse(uploadRes.data);
									} else {
										result = uploadRes.data;
									}
									
									if(result.success && result.file_url) {
										resolve(result.file_url);
									} else {
										console.error('图片上传失败:', result);
										reject(new Error('图片上传失败'));
									}
								} catch(e) {
									console.error('解析上传响应失败:', e, uploadRes);
									reject(e);
								}
							},
							fail: (err) => {
								console.error('图片上传请求失败:', err);
								reject(err);
							}
						});
					});
					
					// 更新或添加到上传URL数组
					if(i < uploadedUrls.length) {
						uploadedUrls[i] = uploadedUrl;
					} else {
						uploadedUrls.push(uploadedUrl);
					}
					
				} catch(err) {
					// 显示上传失败提示
					uni.showToast({
						title: `第${i+1}张图片上传失败`,
						icon: 'none'
					});
				}
			}
			
			this.isUploading = false;
			this.uploadedImageUrls = uploadedUrls;
			return uploadedUrls;
		}
	}
}
