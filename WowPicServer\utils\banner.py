import logging
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import json  # 用于解析及存储JSON到用户notes字段

from config import Config
from utils.auth import get_current_user, get_db
from database.models import User, CoinTransaction, CoinTransactionSource

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/wowpic/banners",
    tags=["轮播图"],
)

# Banner 配置直接定义在路由文件中
BANNERS = [
    {
        "id": 1,
        "image_url": "/static/banners/新人专享50币.png",
        "title": "新人专享50哇图币",
        "description": "点击立即领取！限时福利",
        "action": {
            "type": "REWARD_COINS",
            "payload": {
                "amount": 50,
                "message": "恭喜您获得50哇图币！"
            }
        }
    },
    {
        "id": 2,
        "image_url": "/static/banners/banner2.jpg",
        "title": "人！喵生是旷野！",
        "description": "猫咪去旅行",
        "action": {
            "type": "NAVIGATE",
            "payload": {
                "url": "/pages/generate/generate?style=猫咪去旅行"
            }
        }
    }
]

# =========================
# 已领取记录的持久化策略
# -------------------------
# 为避免新增数据表，也不引入Redis，改用 User.notes(Text) 字段存储JSON。
# 结构示例：
# {
#     "claimed_banners": [1, 2]
# }
# =========================

def _get_claimed_banner_ids(user: User):
    """从 user.notes 中解析已领取的 banner id 列表"""
    try:
        notes_dict = json.loads(user.notes) if user.notes else {}
    except json.JSONDecodeError:
        notes_dict = {}
    return notes_dict.get("claimed_banners", [])


def _add_claimed_banner_id(user: User, banner_id: int):
    """将新的 banner id 添加到 user.notes 中并返回更新后的列表"""
    claimed = _get_claimed_banner_ids(user)
    if banner_id not in claimed:
        claimed.append(banner_id)
    # 更新 notes 字段
    notes_dict = {"claimed_banners": claimed}
    user.notes = json.dumps(notes_dict, ensure_ascii=False)
    return claimed

@router.get("")
async def get_banners():
    """获取所有Banner列表"""
    # 构建完整的图片URL
    response_banners = []
    for banner in BANNERS:
        # 复制一份，避免修改原始数据
        banner_copy = banner.copy()
        # 保持相对路径，前端会根据 baseUrl 自动拼接完整地址
        if not banner_copy["image_url"].startswith("/"):
            banner_copy["image_url"] = "/" + banner_copy["image_url"].lstrip("./")
        # 移除action字段，保留title和description
        banner_copy.pop("action", None)
        response_banners.append(banner_copy)
    
    return response_banners

@router.post("/{banner_id}/click")
async def handle_banner_click(
    banner_id: int, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """处理Banner点击事件"""
    # 查找对应的Banner
    banner = None
    for b in BANNERS:
        if b["id"] == banner_id:
            banner = b
            break
    
    if not banner:
        raise HTTPException(status_code=404, detail="Banner不存在")
    
    action = banner.get("action", {"type": "NONE"})
    action_type = action.get("type")
    
    # 处理奖励哇图币逻辑
    if action_type == "REWARD_COINS":
        # -------------------------
        # 防重复领取：检查 user.notes
        # -------------------------
        claimed_list = _get_claimed_banner_ids(current_user)
        if banner_id in claimed_list:
            logger.info(f"用户 {current_user.id} 重复点击Banner {banner_id}")
            return {"type": "ALREADY_REWARDED", "payload": {"message": "您已领取过该奖励"}}

        # 记录领取
        _add_claimed_banner_id(current_user, banner_id)

        # 增加哇图币
        amount = action.get("payload", {}).get("amount", 0)
        current_user.coins += amount
        # 写入流水
        db.add(
            CoinTransaction(
                user_id=current_user.id,
                change=amount,
                balance=current_user.coins,
                source=CoinTransactionSource.MANUAL,
                remark=f"Banner奖励 {banner_id}",
            )
        )
        db.commit()

        logger.info(f"用户 {current_user.id} 通过Banner {banner_id} 获得 {amount} 哇图币")
    
    return action 