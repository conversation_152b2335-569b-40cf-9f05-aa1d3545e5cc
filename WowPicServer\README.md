# WowPic 后端服务

WowPic AI图片生成小程序的后端服务，基于FastAPI + MySQL开发。

## 功能特性

- ✅ 自动检测和创建数据库
- ✅ 数据表结构严格校验
- ✅ 支持自动更新数据表结构
- ✅ 环境变量配置数据库密码
- ✅ 完整的用户认证系统设计
- ✅ 多平台登录支持（微信、QQ等）
- ✅ AI图片生成任务管理

## 数据库设计

### 核心表结构

1. **users** - 用户主表
   - 存储用户核心信息（昵称、头像、金币等）
   - 支持跨平台用户统一管理

2. **user_authentications** - 用户认证表
   - 存储各平台的身份凭证（openid、unionid）
   - 支持一个用户绑定多个平台账号

3. **styles** - 风格模板表
   - 支持完整提示词和变量化提示词两种模式
   - 灵活的模板变量定义

4. **generations** - 图片生成记录表
   - 完整的任务状态跟踪
   - 支持异步任务处理

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\\Scripts\\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置数据库

```bash
# 复制环境变量配置文件
copy .env.example .env

# 编辑 .env 文件，配置数据库连接信息
# 主要需要修改：
# DB_PASSWORD=你的数据库密码
# DB_NAME=数据库名称
```

### 3. 启动服务

```bash
python main.py
```

服务启动后会自动：
- 检测数据库是否存在，不存在则创建
- 检测数据表是否存在，不存在则创建
- 校验数据表结构，发现问题会打印日志提醒
- 初始化基础数据（默认风格模板）

### 4. 访问服务

- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USER | 数据库用户名 | root |
| DB_PASSWORD | 数据库密码 | 无 |
| DB_NAME | 数据库名称 | wowpic |
| AUTO_UPDATE_DB_SCHEMA | 自动更新表结构 | False |

### 数据库结构校验

系统会在启动时自动校验数据表结构，包括：
- 字段是否存在
- 字段类型是否匹配
- 约束条件是否正确
- 索引是否完整

如果发现问题：
1. **AUTO_UPDATE_DB_SCHEMA=False**: 只打印日志提醒，需要手动修复
2. **AUTO_UPDATE_DB_SCHEMA=True**: 尝试自动修复（谨慎使用）

## 开发说明

### 项目结构

```
WowPicServer/
├── main.py                 # 应用入口
├── config.py              # 配置管理
├── requirements.txt       # 依赖列表
├── .env                   # 环境变量（需要创建）
├── .env.example          # 环境变量示例
└── database/             # 数据库模块
    ├── __init__.py
    ├── models.py         # 数据模型定义
    ├── connection.py     # 数据库连接管理
    ├── schema_validator.py # 结构校验器
    └── initializer.py    # 数据库初始化器
```

### 添加新的数据表

1. 在 `database/models.py` 中定义新的模型类
2. 重启应用，系统会自动创建新表
3. 如需修改现有表结构，建议先备份数据

## 注意事项

⚠️ **重要提醒**：
- 生产环境请务必修改 JWT_SECRET_KEY
- 数据库密码等敏感信息通过环境变量配置
- 自动更新表结构功能请谨慎使用
- 建议在修改表结构前备份数据库