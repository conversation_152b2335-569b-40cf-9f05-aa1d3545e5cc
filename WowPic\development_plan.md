# WowPic AI图片生成小程序开发方案

## 1. 项目概述

WowPic 是一款基于 AI 技术的图片生成小程序。用户可以上传自己的照片，选择不同的艺术风格（如吉卜力、赛博朋克等），并输入自定义的提示词（Prompt），由 AI 引擎将原始照片转换成具有特定艺术风格的全新图片。项目旨在为用户提供有趣、富有创造力的 AI 图片玩法。

本项目包含小程序前端、业务后端两大模块。

- **前端**：使用 Uni-app 框架开发，可一套代码发布到微信小程序、iOS、Android 等多个平台。
- **后端**：使用 Python (建议 FastAPI 或 Flask 框架) + MySQL 进行开发，负责业务逻辑、用户管理和与 AI 模型交互。所有API路由将以 `/wowpic` 作为前缀。

## 2. 前端开发方案 (Uni-app)

### 2.1. 技术栈

- **框架**: Vue.js + Uni-app
- **UI库**: 无，使用原生CSS或Sass进行样式开发
- **状态管理**: Vuex 或 Pinia (对于中小型项目，也可使用 uni-app 的 `globalData` 或 `eventBus`)
- **HTTP请求**: `uni.request`

### 2.2. 页面功能逻辑拆解

#### 2.2.1. 首页 (`pages/index/index.vue`)

- **核心功能**: 展示热门风格，引导用户进行创作。
- **UI 逻辑**:
    1.  **用户信息区**: 页面加载时，从本地缓存或通过后端接口获取用户昵称和头像并展示。
    2.  **金币余额区**: 从后端接口获取当前用户的 "哇图币" 余额并展示。
    3.  **功能广告卡片**: 可由后端配置，动态展示活动或新功能入口。
    4.  **热门风格列表**: 从后端接口获取热门风格列表（包含风格名称、描述、封面图），并以网格形式展示。
- **交互逻辑**:
    - 点击任意风格卡片，调用 `uni.navigateTo` 跳转到 `pages/generate/generate` 页面，并通过 URL 参数传递被点击的风格 `ID` 或 `名称`。
- **接口需求**:
    - `GET /wowpic/user/profile`: 获取用户简要信息（昵称、头像）。
    - `GET /wowpic/user/balance`: 获取用户哇图币余额。
    - `GET /wowpic/styles/popular`: 获取热门风格列表。

#### 2.2.2. 图片生成页 (`pages/generate/generate.vue`)

- **核心功能**: 根据所选风格，动态展示输入项，用户上传图片、填写所需信息后，调用AI服务生成图片。
- **UI 逻辑**:
    1.  **获取风格详情**: 页面 `onLoad` 时，根据传入的 `styleId`，从后端 `GET /wowpic/styles/{styleId}` 接口获取风格的详细定义，包括其 `template_type` 和 `template_variables`。
    2.  **动态渲染输入项**:
        -   如果 `template_type` 为 `FULL_PROMPT`，则显示一个大的 `textarea` 让用户自由输入提示词。
        -   如果 `template_type` 为 `VARIABLE_PROMPT`，则根据 `template_variables` (一个JSON数组) 动态生成一系列输入框。例如，`[{"name": "location", "label": "旅行地点", "type": "text"}, ...]` 将会渲染出一个标签为"旅行地点"的文本输入框。
    3.  **条件性图片上传**: 根据风格定义，决定是否需要上传图片。
    4.  **结果展示**: 逻辑不变，生成成功后在下方展示结果图和操作按钮。
- **交互逻辑**:
    1.  点击 **生成图片** 按钮，触发 `generateImage` 方法。
    2.  **数据收集**: 根据当前风格的类型，收集是完整的提示词，还是一个包含各个变量值的对象（如 `{"location": "巴黎", "character": "爱因斯坦"}`）。
    3.  **前端校验**: 检查必填项是否已填写，需要上传图片的模板是否已上传图片。
    4.  **上传原图 (如果需要)**: 调用 `uni.uploadFile` 上传图片，获取URL。
    5.  **调用生成接口**: 调用后端 `POST /wowpic/image/generate` 接口，参数为 `{ "styleId": 1, "sourceImageUrl": "url", "inputs": "a full prompt here" | {"location": "巴黎"} }`。
    6.  **轮询结果**: 调用 `GET /wowpic/image/task/{taskId}` 接口轮询任务状态。
    7.  **处理和保存**: 逻辑不变。
- **接口需求**:
    - `GET /wowpic/styles/{styleId}`: 获取指定风格的详细配置。
    - `POST /wowpic/image/generate`: 创建图片生成任务（接口定义见后端部分）。
    - `GET /wowpic/image/task/{taskId}`: 查询生成任务的状态和结果。

#### 2.2.3. 个人中心页 (`pages/profile/profile.vue`)

- **核心功能**: 展示用户信息、账户资产、快捷功能和历史作品。
- **UI 逻辑**:
    1.  **用户信息卡片**: 获取并展示用户的详细信息，如头像、昵称、用户ID。
    2.  **快捷功能区**: 展示如 "每日签到"、"邀请有礼" 等功能入口。
    3.  **我的作品**: 从后端接口分页获取用户生成的历史作品列表，并以瀑布流或网格布局展示。
- **交互逻辑**:
    - **下拉刷新/上拉加载**: 实现作品列表的下拉刷新和上拉加载更多。
    - **每日签到**: 点击后调用后端签到接口，并根据结果给与用户反馈。
    - **作品点击**: 点击作品图片可跳转到作品详情页（或实现预览大图）。
- **接口需求**:
    - `GET /wowpic/user/profile`: 获取用户详细信息。
    - `GET /wowpic/user/works?page=1&limit=10`: 分页获取用户的作品列表。
    - `POST /wowpic/user/check-in`: 执行每日签到操作。

### 2.3. 通用逻辑

- **登录/认证**:
    1.  在 `App.vue` 的 `onLaunch` 中调用 `uni.login()` 获取 `code`。
    2.  将 `code` 和 `platform` ('WX', 'QQ' 等) 发送到后端 `POST /wowpic/auth/login` 接口。
    3.  后端根据平台信息处理登录逻辑，返回 JWT Token。
    4.  前端将 Token 存储在 `uni.setStorageSync`。
    5.  后续所有需要授权的请求，在请求头中带上此 Token。
- **请求封装**: 在 `utils` 目录下封装一个 `request.js` 模块，统一处理API前缀 (`/wowpic`)、请求头、Token携带、错误码处理和 `uni.showLoading` 等逻辑。

## 3. 后端开发方案 (Python + MySQL)

### 3.1. 技术栈

- **Web框架**: FastAPI (推荐，性能高，自带文档) 或 Flask
- **数据库 ORM**: SQLAlchemy (配合FastAPI/Flask) 或直接使用 `mysql-connector-python`
- **异步任务队列**: Celery + Redis (处理耗时的 AI 生成任务)
- **AI模型服务**: 可对接第三方API（如 Midjourney, Stable Diffusion API）或自建模型服务。

### 3.2. 核心架构设计

采用 **API服务 + 异步任务队列** 的模式，避免 AI 图片生成等耗时操作阻塞主应用。

1.  **API Server (FastAPI)**: 接收前端的 HTTP 请求，负责业务逻辑处理，如用户认证、数据查询、创建生成任务等。
2.  **Task Queue (Celery)**: 负责执行耗时任务。API Server 接收到生成请求后，不是立即执行，而是将任务参数（如 userId, prompt, style等）放入 Redis 消息队列中，并立即返回一个 `taskId` 给前端。
3.  **Worker (Celery Worker)**: 独立进程，持续监控任务队列。一旦发现新任务，就取出并执行。执行过程包括：调用AI模型、等待结果、将生成好的图片存入对象存储、更新数据库中的任务状态。

### 3.3. API 接口设计

#### `/wowpic/auth` (认证模块)
- `POST /login`
  - **功能**: 多平台用户登录或注册。
  - **请求**: `{ "code": "platform_login_code", "platform": "WX" | "QQ" }`
  - **逻辑**: 根据 `platform` 调用对应平台的接口换取 openid/uid，通过 unionid (如果可用) 或 openid 查询或创建用户及认证记录，生成 JWT Token。
  - **响应**: `{ "token": "jwt_token", "isNewUser": true/false }`

#### `/wowpic/user` (用户模块)
- `GET /profile`
  - **功能**: 获取当前登录用户的详细信息。
  - **请求**: Header: `Authorization: Bearer <token>`
  - **响应**: 返回 `users` 表中的非敏感信息。
- `GET /works`
  - **功能**: 分页获取用户的作品 (只返回 `is_visible=true` 的记录)。
  - **请求**: Header: `Authorization: Bearer <token>`, Query: `page`, `limit`
  - **响应**: `{ "items": [ ... ], "total": 100 }`
- `POST /check-in`
  - **功能**: 用户每日签到。
  - **逻辑**: 检查 `users.last_checkin_date`，如果不是今天，则增加金币并更新该字段。
  - **响应**: `{ "success": true, "reward": 5 }`

#### `/wowpic/styles` (风格模块)
- `GET /popular`
  - **功能**: 获取热门风格列表。
  - **响应**: `[{ "id": 1, "name": "吉卜力风格", ... }]` (只包含列表所需的基本信息)
- `GET /{styleId}`
  - **功能**: 获取单个风格的详细信息，用于生成页。
  - **响应**: 返回 `styles` 表中该风格的完整记录，包括 `template_type` 和 `template_variables`。

#### `/wowpic/image` (图片生成模块)
- `POST /generate`
  - **功能**: 提交一个图片生成任务。
  - **请求**: `{ "styleId": 1, "sourceImageUrl": "url" (optional), "inputs": "a full prompt here" | {"location": "巴黎", "character": "猫咪"} }`
  - **逻辑**:
    1.  验证用户并扣除金币。
    2.  从数据库查询 `styleId` 对应的风格定义。
    3.  如果风格是 `VARIABLE_PROMPT`，使用 `inputs` 对象和 `prompt_template` 来格式化最终的提示词。
    4.  在 `generations` 表创建记录，状态为 `PENDING`。
    5.  将任务（包含最终提示词、原图URL等）推送到 Celery 队列。
  - **响应**: `{ "taskId": "celery_task_id" }`
- `GET /task/{taskId}`
  - **功能**: 查询任务状态。
  - **请求**: Path: `taskId`
  - **逻辑**: 从 `generations` 表查询任务记录。
  - **响应**: `{ "status": "SUCCESS/PROCESSING/FAILED", "generatedImageUrl": "url" (if success) }`

### 3.4. 服务层 (Services) 设计

为了实现业务逻辑与API路由的清晰分离，我们将引入一个服务层 (Service Layer)。API接口层将不再直接操作数据库或执行复杂逻辑，而是调用服务层中的函数来完成工作。

- **目的与原则**:
    - **逻辑解耦**: 将所有业务逻辑（如用户认证、数据增删改查、条件判断、金币操作等）从路由处理函数中剥离出来。
    - **代码复用**: 多个API接口可以复用同一个服务函数。例如，获取用户信息的逻辑可以在多个需要用户数据的地方被调用。
    - **可测试性**: 服务层的函数是纯粹的Python函数，不依赖于Web请求上下文，因此可以被轻松地进行单元测试。

- **目录结构**:
    - 所有服务层代码将存放在 `WowPicServer/services/` 目录下。

- **服务拆分与函数定义**:

    - **`auth_service.py` - 认证服务**
        - `get_or_create_user_by_platform(db, platform, platform_uid, platform_unionid, default_nickname, default_avatar)`: 根据平台ID查找用户，如果不存在则创建新用户及其认证记录。这是登录和注册的核心逻辑。
        - `generate_jwt_token(user_id)`: 为指定用户ID生成JWT Token。

    - **`user_service.py` - 用户服务**
        - `get_user_profile(db, user_id)`: 获取用户的详细档案信息。
        - `get_user_works(db, user_id, page, limit)`: 分页获取用户的可见作品列表。
        - `handle_daily_check_in(db, user_id)`: 处理每日签到逻辑，返回签到结果和奖励金币数量。
        - `deduct_coins(db, user_id, amount)`: 扣除用户金币，并进行余额是否充足的检查。

    - **`style_service.py` - 风格服务**
        - `get_popular_styles(db)`: 获取被标记为热门的风格列表，用于首页展示。
        - `get_style_details(db, style_id)`: 获取单个风格的完整定义，用于生成页面。

    - **`generation_service.py` - 图片生成服务**
        - `create_generation_task(db, user_id, style_id, source_image_url, inputs)`: 创建一个完整的图片生成任务。此函数将编排以下操作：
            1.  调用 `style_service.get_style_details` 获取风格信息和所需费用。
            2.  调用 `user_service.deduct_coins` 扣除用户金币。
            3.  根据风格的 `template_type` 和 `inputs`，构建最终的 `prompt`。
            4.  在 `generations` 表中创建一条状态为 `PENDING` 的记录。
            5.  将任务信息（如记录ID、最终prompt等）发送到 Celery 任务队列。
            6.  返回 `task_id` 给API层。
        - `get_task_by_id(db, task_id)`: 根据 `task_id` 查询生成任务的当前状态和结果。

## 4. 数据库设计 (MySQL)

**重要说明**: 为了更好地支持跨平台，用户系统将分为两张表：`users` (主用户表) 和 `user_authentications` (用户认证凭证表)。这种设计可以轻松地将一个用户的多个平台账号（微信、QQ等）关联到同一个主账户下。

**设计理念与解答**:

1.  **为什么是"主表+认证子表"的结构？**
    -   **高扩展性**: 未来接入新平台（如微博、抖音）登录，只需在 `user_authentications` 表中增加新的认证记录，无需修改任何已有的表结构，风险低，易于维护。
    -   **职责清晰**: `users` 表只存储用户的核心、通用信息（如昵称、资产），而 `user_authentications` 表专门负责"如何认证这个用户"，实现了业务数据和认证数据的分离。
    -   **避免数据稀疏**: 单表方案会导致大量的 `NULL` 字段（如 `wx_openid`, `qq_openid`...），而子表方案则保证了每条记录都是有效数据。

2.  **OpenID 和 UnionID 如何存放？**
    -   **`openid`**: 作为平台内应用的唯一标识，它应该存放在 `user_authentications` 表的 `platform_uid` 字段中，并与 `platform` 字段 ('WX', 'QQ' 等) 组合使用，确保唯一性。
    -   **`unionid`**: 作为跨越平台内所有应用的唯一用户标识，它应该存放在 `users` 主表中。它是关联来自同一开放平台（如微信开放平台）下不同应用（小程序、网站、App）用户的"黄金钥匙"。

3.  **登录与关联逻辑**:
    -   当用户登录时，后端应优先尝试使用 `unionid` 在 `users` 表中查找用户。如果找到，说明用户已存在，只需关联新的 `openid` 即可。
    -   如果 `unionid` 找不到或平台不提供，再使用 `openid` 在 `user_authentications` 表中查找。
    -   如果都找不到，则为新用户，创建 `users` 和 `user_authentications` 记录。
    -   后续可以通过绑定手机号或在已有账号上授权获取 `unionid` 的方式，来合并不同时期创建的、属于同一个人的账户。

### 4.1. `users` - 用户表

存储用户的核心档案信息，独立于任何特定平台。

| 字段名          | 类型          | 约束/备注                   |
| --------------- | ------------- | --------------------------- |
| `id`            | `INT`         | `PK`, `AUTO_INCREMENT`      |
| `nickname`      | `VARCHAR(255)`| 用户昵称 (可来自首次注册的平台) |
| `avatar_url`    | `VARCHAR(512)`| 用户头像URL                 |
| `phone`         | `VARCHAR(20)` | `UNIQUE`, `NULLABLE`, 手机号, **核心关联字段** |
| `coins`         | `INT`         | `NOT NULL`, `DEFAULT 0`, 哇图币余额 |
| `last_checkin_date` | `DATE`    | `NULLABLE`, 上次签到日期    |
| `last_login_at` | `DATETIME`    | `NULLABLE`, 上次登录时间 |
| `is_banned`     | `BOOLEAN`     | `NOT NULL`, `DEFAULT FALSE`, 是否被封禁 |
| `notes`         | `TEXT`        | `NULLABLE`, 管理员备注 |
| `created_at`    | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP` |
| `updated_at`    | `DATETIME`    | `ON UPDATE CURRENT_TIMESTAMP`|

### 4.2. `user_authentications` - 用户认证表 (新增)

存储用户在不同平台的身份凭证。

| 字段名 | 类型 | 约束/备注 |
| --- | --- | --- |
| `id` | `INT` | `PK`, `AUTO_INCREMENT` |
| `user_id` | `INT` | `FK` -> `users.id`, 关联到主用户 |
| `platform` | `VARCHAR(20)` | `NOT NULL`, 如 'WX', 'QQ', 'WEB' |
| `platform_uid`| `VARCHAR(128)` | `NOT NULL`, 即各平台的 openid |
| `platform_unionid`| `VARCHAR(128)` | `NULLABLE`, 存放该平台的 unionid |
| `created_at` | `DATETIME` | `DEFAULT CURRENT_TIMESTAMP` |
| *约束* | | `UNIQUE(platform, platform_uid)` |
| *索引* | | `INDEX(platform, platform_unionid)` |

### 4.3. `styles` - 风格表

存储可用的 AI 图片风格，并增加了模板化支持。

| 字段名          | 类型          | 约束/备注                   |
| --------------- | ------------- | --------------------------- |
| `id`            | `INT`         | `PK`, `AUTO_INCREMENT`      |
| `name`          | `VARCHAR(100)`| `UNIQUE`, `NOT NULL`, 风格名称 |
| `description`   | `VARCHAR(255)`| 风格简短描述                |
| `cover_image_url` | `VARCHAR(512)`| 风格封面图URL               |
| `template_type` | `ENUM('FULL_PROMPT', 'VARIABLE_PROMPT')` | `NOT NULL`, 提示词类型 |
| `prompt_template` | `TEXT`      | `NULLABLE`, 变量提示词的模板，如 `A photo of {character} in {location}` |
| `template_variables`| `JSON`    | `NULLABLE`, 定义变量的输入控件, e.g., `[{"name":"character", "label":"人物"},...]` |
| `model_identifier`| `VARCHAR(100)`| `NOT NULL`, 内部AI模型标识符 |
| `cost`          | `INT`         | `NOT NULL`, `DEFAULT 5`, 每次消耗金币 |
| `is_popular`    | `BOOLEAN`     | `DEFAULT FALSE`, 是否为热门 |
| `created_at`    | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP` |

### 4.4. `generations` - 图片生成记录表

存储用户的每一次图片生成任务和结果。

| 字段名          | 类型                                 | 约束/备注                        |
| --------------- | ------------------------------------ | -------------------------------- |
| `id`            | `BIGINT`                             | `PK`, `AUTO_INCREMENT`, 自增主键  |
| `task_id`       | `VARCHAR(255)`                       | `UNIQUE`, `NOT NULL`, Celery Task ID |
| `user_id`       | `INT`                                | `FK` -> `users.id`               |
| `style_id`      | `INT`                                | `FK` -> `styles.id`              |
| `source_image_url` | `VARCHAR(512)`                    | `NULLABLE`, 用户上传的原图URL    |
| `prompt`        | `TEXT`                               | `NULLABLE`, 最终生成时使用的完整提示词 |
| `status`        | `ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED')` | `NOT NULL`, `DEFAULT 'PENDING'` |
| `generated_image_url` | `VARCHAR(512)`                  | `NULLABLE`, AI生成的结果图URL     |
| `is_visible`    | `BOOLEAN`                            | `NOT NULL`, `DEFAULT TRUE`, 是否对用户可见/是否屏蔽 |
| `cost`          | `INT`                                | `NOT NULL`, 本次消耗的金币       |
| `error_message` | `TEXT`                               | `NULLABLE`, 失败原因             |
| `created_at`    | `DATETIME`                           | `DEFAULT CURRENT_TIMESTAMP`      |
| `completed_at`  | `DATETIME`                           | `NULLABLE`, 任务完成时间         |

## 5. 开发与部署建议

- **代码管理**: 使用 Git 进行版本控制，建议创建 `frontend` 和 `backend` 两个目录分别存放前后端代码。
- **对象存储**: 用户上传的图片和AI生成的图片建议存放在云对象存储服务（如阿里云OSS、腾讯云COS、AWS S3），而不是直接存在服务器磁盘上，便于管理和CDN加速。
- **部署**: 
    - **后端**: 使用 Docker 将 FastAPI/Flask 应用、Celery Worker 打包成镜像，部署在云服务器上。使用 Gunicorn/Uvicorn 作为应用服务器，并用 Nginx 作为反向代理。
    - **数据库**: 使用云数据库服务（如 AWS RDS, 阿里云 RDS），便于管理和备份。
    - **前端**: 通过 HBuilderX 或微信开发者工具，将代码编译并上传到小程序平台进行审核和发布。 