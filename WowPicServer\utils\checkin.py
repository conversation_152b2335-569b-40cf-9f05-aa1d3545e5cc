import logging
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from config import Config
from utils.auth import get_current_user, get_db
from database.models import User, CoinTransaction, CoinTransactionSource

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/wowpic/checkin",
    tags=["签到"],
)

# 签到奖励配置
DAILY_CHECKIN_REWARD = 20  # 每日签到奖励20哇图币

@router.post("/daily")
async def daily_checkin(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """每日签到接口，每天可签到一次，获得奖励金币"""
    today = date.today()
    
    # 检查今天是否已签到
    if current_user.last_checkin_date and current_user.last_checkin_date == today:
        return {
            "success": False,
            "message": "今天已经签到过了",
            "coins": current_user.coins
        }
    
    # 更新签到记录和增加金币
    current_user.last_checkin_date = today
    current_user.coins += DAILY_CHECKIN_REWARD
    # 写入流水
    db.add(
        CoinTransaction(
            user_id=current_user.id,
            change=DAILY_CHECKIN_REWARD,
            balance=current_user.coins,
            source=CoinTransactionSource.CHECKIN,
            remark="每日签到奖励",
        )
    )
    db.commit()
    
    logger.info(f"用户 {current_user.id} 完成每日签到，获得 {DAILY_CHECKIN_REWARD} 哇图币")
    
    return {
        "success": True,
        "message": f"签到成功！获得 {DAILY_CHECKIN_REWARD} 哇图币",
        "coins": current_user.coins,
        "reward": DAILY_CHECKIN_REWARD  # 添加奖励数量字段
    }

@router.get("/status")
async def check_signin_status(
    date_str: str = Query(None, description="检查的日期，格式为YYYY-MM-DD，默认为今天"),
    current_user: User = Depends(get_current_user)
):
    """检查用户在指定日期是否已签到"""
    # 如果没有提供日期，默认为今天
    check_date = date.today()
    if date_str:
        try:
            check_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        except ValueError:
            return {"success": False, "message": "日期格式错误，请使用YYYY-MM-DD格式"}
    
    # 检查用户是否已在指定日期签到
    is_signed_in = current_user.last_checkin_date == check_date
    
    return {
        "success": True,
        "is_signed_in": is_signed_in,
        "check_date": str(check_date)
    } 