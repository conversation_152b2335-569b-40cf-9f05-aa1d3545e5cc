import logging
import time
import uuid
import json
import base64
import hashlib
import random
from pathlib import Path
from datetime import datetime
from typing import List

# ---------------------------------
# 充值档位配置
# key: 充值后获得的哇图币数量 (coins)
# value: 需要支付的人民币金额（元）
# ---------------------------------
# 包含一档 0.01 元测试挡位
# 实际销售挡位：
#   ¥0.99 -> 100  币  (无赠送)
#   ¥6    -> 630  币  (约 +5%)
#   ¥29.9 -> 3300 币  (约 +10%)
#   ¥69.8 -> 8050 币  (约 +15%)

PRICE_MAP = {
    # 1: 0.01,       # 测试挡位：¥0.01 -> 1 币
    100: 0.99,     # ¥0.99 -> 100 币
    630: 6.0,      # ¥6    -> 630 币
    3300: 29.9,    # ¥29.9 -> 3,300 币
    8050: 69.8,    # ¥69.8 -> 8,050 币
}

import httpx
from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel
from sqlalchemy.orm import Session

from config import Config
from utils.auth import get_current_user, get_db
from database.models import (
    User,
    UserAuthentication,
    RechargeOrder,
    RechargeOrderStatus,
    CoinTransaction,
    CoinTransactionSource,
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/wowpic/pay",
    tags=["支付"],
)


# ----------------------------
# 工具函数
# ----------------------------


def _load_private_key():
    """加载 PEM 私钥内容"""
    key_path = Path(Config.WECHAT_PRIVATE_KEY_PATH)
    if not key_path.exists():
        raise RuntimeError("未找到微信商户私钥文件，请检查 Config.WECHAT_PRIVATE_KEY_PATH")
    return key_path.read_text()


def _rsa_sign(message: str) -> str:
    """使用商户私钥对消息进行 SHA256withRSA 签名，返回 base64 字符串"""
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.asymmetric import padding, rsa
    from cryptography.hazmat.primitives import serialization

    private_key_pem = _load_private_key().encode()
    private_key = serialization.load_pem_private_key(private_key_pem, password=None)

    signature = private_key.sign(
        message.encode(),
        padding.PKCS1v15(),
        hashes.SHA256(),
    )
    return base64.b64encode(signature).decode()


def _build_authorization(method: str, url_path: str, body: str, nonce_str: str, timestamp: str) -> str:
    """生成微信支付V3 Authorization头"""
    message = f"{method}\n{url_path}\n{timestamp}\n{nonce_str}\n{body}\n"
    signature = _rsa_sign(message)
    token = (
        "WECHATPAY2-SHA256-RSA2048 "
        f"mchid=\"{Config.WECHAT_MCH_ID}\"," \
        f"nonce_str=\"{nonce_str}\"," \
        f"timestamp=\"{timestamp}\"," \
        f"serial_no=\"{Config.WECHAT_MCH_SERIAL_NO}\"," \
        f"signature=\"{signature}\""
    )
    return token


def _build_front_pay_params(prepay_id: str, appid: str) -> dict:
    """生成小程序端 requestPayment 需要的参数"""
    ts = str(int(time.time()))
    nonce_str = uuid.uuid4().hex[:32]
    pkg = f"prepay_id={prepay_id}"
    # 支付签名串
    msg = f"{appid}\n{ts}\n{nonce_str}\n{pkg}\n"
    pay_sign = _rsa_sign(msg)
    return {
        "timeStamp": ts,
        "nonceStr": nonce_str,
        "package": pkg,
        "signType": "RSA",
        "paySign": pay_sign,
    }


# ----------------------------
# 请求模型
# ----------------------------


class RechargeRequest(BaseModel):
    amount: int  # 充值哇图币数量 (30/50/100/200/500)


class RechargeResponse(BaseModel):
    success: bool
    payment_params: dict


# ----------------------------
# 新增：充值档位查询接口
# ----------------------------


class RechargeOption(BaseModel):
    coins: int
    price_cny: float


# =========================
# 1元充值限制的持久化策略
# -------------------------
# 参考banner系统的实现，使用 User.notes(Text) 字段存储JSON。
# 结构示例：
# {
#     "claimed_banners": [1, 2],
#     "used_one_yuan_recharge": true  # 是否已使用过1元充值
# }
# =========================

def _has_used_one_yuan_recharge(user: User) -> bool:
    """检查用户是否已使用过1元充值(100币)

    Args:
        user: 用户对象

    Returns:
        bool: 如果用户已使用过1元充值返回True，否则返回False

    Note:
        如果解析出现异常，为了不影响充值流程，返回False（允许充值）
    """
    try:
        notes_dict = json.loads(user.notes) if user.notes else {}
        return notes_dict.get("used_one_yuan_recharge", False)
    except json.JSONDecodeError:
        # JSON解析失败，返回False（允许充值）
        logger.warning(f"用户 {user.id} 的notes字段JSON解析失败，默认允许1元充值")
        return False
    except Exception as e:
        # 其他异常，记录错误但不抛出异常，确保不影响充值流程
        logger.warning(f"检查用户1元充值历史时发生错误，用户ID: {user.id}, 错误: {e}")
        return False


def _mark_one_yuan_recharge_used(user: User):
    """标记用户已使用过1元充值

    Args:
        user: 用户对象
    """
    try:
        notes_dict = json.loads(user.notes) if user.notes else {}
    except json.JSONDecodeError:
        # JSON解析失败，重新创建
        notes_dict = {}

    # 标记已使用1元充值
    notes_dict["used_one_yuan_recharge"] = True
    user.notes = json.dumps(notes_dict, ensure_ascii=False)


@router.get("/options", response_model=List[RechargeOption])
async def get_recharge_options(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """返回当前可用的充值档位列表

    对于已使用过1元充值(100币)的用户，将不再显示1元充值选项
    """
    try:
        # 获取所有充值选项
        all_options = [{"coins": c, "price_cny": p} for c, p in PRICE_MAP.items()]

        # 检查用户是否已使用过1元充值(100币)
        has_used_one_yuan = _has_used_one_yuan_recharge(current_user)

        # 如果用户已使用过1元充值，则过滤掉100币的选项
        if has_used_one_yuan:
            filtered_options = [opt for opt in all_options if opt["coins"] != 100]
            logger.info(f"用户 {current_user.id} 已使用过1元充值，过滤后返回 {len(filtered_options)} 个充值选项")
            return filtered_options

        logger.info(f"用户 {current_user.id} 未使用过1元充值，返回所有 {len(all_options)} 个充值选项")
        return all_options

    except Exception as e:
        # 如果出现任何异常，返回所有选项以确保充值功能正常
        logger.error(f"获取充值选项时发生错误，用户ID: {current_user.id}, 错误: {e}")
        all_options = [{"coins": c, "price_cny": p} for c, p in PRICE_MAP.items()]
        return all_options


# ----------------------------
# 主要逻辑
# ----------------------------


@router.post("/recharge", response_model=RechargeResponse)
async def create_recharge_order(
    req: RechargeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """创建充值订单，返回前端调用 wx.requestPayment 的参数"""

    # 使用模块级 PRICE_MAP 定义可充值档位及对应金额

    if req.amount not in PRICE_MAP:
        raise HTTPException(status_code=400, detail="不支持的充值金额")

    # DEBUG模式下直接返回模拟数据，便于本地调试
    if Config.DEBUG:
        logger.warning("DEBUG 模式启用，返回模拟支付参数，不会真正调用微信接口")
        ts = str(int(time.time()))
        nonce = uuid.uuid4().hex
        fake_sign = hashlib.md5(f"{ts}{nonce}".encode()).hexdigest()
        return {
            "success": True,
            "payment_params": {
                "timeStamp": ts,
                "nonceStr": nonce,
                "package": "prepay_id=MOCK_PREPAY_ID",
                "signType": "MD5",
                "paySign": fake_sign,
            },
        }

    # 计算人民币金额
    total_cents = int(PRICE_MAP[req.amount] * 100)  # 转为分

    # 获取用户 openid
    auth = (
        db.query(UserAuthentication)
        .filter(UserAuthentication.user_id == current_user.id, UserAuthentication.platform == "WX")
        .first()
    )
    if not auth:
        raise HTTPException(status_code=400, detail="找不到用户微信openid，无法发起支付")

    openid = auth.platform_uid

    # 生成商户订单号（32位）
    out_trade_no = uuid.uuid4().hex[:32]

    # 请求体
    body = {
        "appid": Config.WX_APPID,
        "mchid": Config.WECHAT_MCH_ID,
        "description": f"充值{req.amount}哇图币",
        "out_trade_no": out_trade_no,
        "notify_url": Config.WECHAT_PAY_NOTIFY_URL,
        "attach": str(req.amount),  # 自定义数据：充值币数
        "amount": {"total": total_cents, "currency": "CNY"},
        "payer": {"openid": openid},
    }

    body_str = json.dumps(body, separators=(",", ":"))

    nonce_str = uuid.uuid4().hex
    timestamp = str(int(time.time()))

    token = _build_authorization("POST", "/v3/pay/transactions/jsapi", body_str, nonce_str, timestamp)

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": token,
        "User-Agent": "WowPicServer/1.0",
    }

    async with httpx.AsyncClient(timeout=10.0) as client:
        resp = await client.post(
            "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi",
            headers=headers,
            content=body_str,
        )

    if resp.status_code != 200 and resp.status_code != 201:
        logger.error(f"微信下单失败: {resp.status_code}, {resp.text}")
        raise HTTPException(status_code=500, detail="微信下单失败，请稍后重试")

    data = resp.json()
    prepay_id = data.get("prepay_id")
    if not prepay_id:
        logger.error(f"未获取到 prepay_id: {data}")
        raise HTTPException(status_code=500, detail="微信下单失败，缺少prepay_id")

    # 构造前端拉起支付参数
    pay_params = _build_front_pay_params(prepay_id, Config.WX_APPID)

    # 不再在下单阶段写入数据库，等待微信支付成功回调后再入库

    logger.info(
        f"用户 {current_user.id} 请求充值 {req.amount} 哇图币，下单成功，out_trade_no={out_trade_no}, prepay_id={prepay_id}"
    )

    return {"success": True, "payment_params": pay_params}

# ----------------------------
# 支付结果回调（占位实现）
# ----------------------------


@router.post("/notify")
async def pay_notify(request: Request, db: Session = Depends(get_db)):
    """微信支付成功回调 (仅示例，未做报文签名和解密校验)"""
    try:
        body_bytes = await request.body()
        body_json = json.loads(body_bytes)
        logger.info(f"收到支付回调: {body_json}")

        # 校验事件类型
        if body_json.get("event_type") != "TRANSACTION.SUCCESS":
            return {"code": "SUCCESS", "message": "非支付成功事件"}

        # 资源解密
        resource = body_json["resource"]
        ciphertext = base64.b64decode(resource["ciphertext"])
        nonce = resource["nonce"].encode()
        associated_data = resource.get("associated_data", "").encode()

        from cryptography.hazmat.primitives.ciphers.aead import AESGCM

        aesgcm = AESGCM(Config.WECHAT_API_V3_KEY.encode())
        try:
            plaintext = aesgcm.decrypt(nonce, ciphertext, associated_data)
        except Exception as e:
            logger.error(f"回调解密失败: {e}")
            return {"code": "FAIL", "message": "DECRYPT_FAIL"}

        transaction = json.loads(plaintext)
        logger.info(f"解密后交易数据: {transaction}")

        # 获取 attach 字段（充值哇图币数量）
        attach = transaction.get("attach")
        coins = int(attach) if attach and attach.isdigit() else None

        # 获取 openid
        payer_openid = transaction.get("payer", {}).get("openid")

        if coins is None or not payer_openid:
            logger.error("回调缺少必要信息，无法入账")
            return {"code": "FAIL", "message": "MISSING_INFO"}

        # 找到用户并增加哇图币
        from database.models import User, UserAuthentication

        auth = db.query(UserAuthentication).filter(
            UserAuthentication.platform == "WX",
            UserAuthentication.platform_uid == payer_openid,
        ).first()

        if not auth:
            logger.error(f"未找到对应用户，openid={payer_openid}")
            return {"code": "FAIL", "message": "USER_NOT_FOUND"}

        user = auth.user

        # 根据 out_trade_no 查找订单并锁行，防止重复处理
        out_trade_no = transaction.get("out_trade_no")
        order = None
        if out_trade_no:
            order = (
                db.query(RechargeOrder)
                .filter(RechargeOrder.out_trade_no == out_trade_no)
                .with_for_update()
                .first()
            )

        # 若已处理直接返回
        if order and order.status == RechargeOrderStatus.PAID:
            return {"code": "SUCCESS", "message": "OK"}

        # 更新或补写订单
        if order:
            order.status = RechargeOrderStatus.PAID
            order.pay_time = datetime.utcnow()
        else:
            order = RechargeOrder(
                user_id=user.id,
                out_trade_no=out_trade_no or uuid.uuid4().hex[:32],
                prepay_id=transaction.get("prepay_id"),
                coins=coins,
                amount_cny=transaction.get("amount", {}).get("total", 0),
                status=RechargeOrderStatus.PAID,
                pay_time=datetime.utcnow(),
            )
            db.add(order)

        # 写入流水并加币
        user.coins += coins
        db.add(
            CoinTransaction(
                user_id=user.id,
                change=coins,
                balance=user.coins,
                source=CoinTransactionSource.RECHARGE,
                source_id=order.id,
                remark=f"微信充值订单{order.out_trade_no}",
            )
        )

        # 记录用户已使用过1元充值（仅当充值100币时）
        if coins == 100:
            _mark_one_yuan_recharge_used(user)

        db.commit()

        logger.info(f"用户 {user.id} 充值成功 +{coins} 哇图币，余额 {user.coins}，已记录充值历史")

        return {"code": "SUCCESS", "message": "OK"}
    except Exception as e:
        logger.error(f"处理支付回调异常: {e}")
        return {"code": "FAIL", "message": "ERROR"} 