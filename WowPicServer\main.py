from fastapi import FastAPI
from contextlib import asynccontextmanager
import uvicorn
import logging
from database.initializer import db_initializer
from config import Config
import colorama
from colorama import Fore, Style
from fastapi.staticfiles import StaticFiles
import sys  # 新增：用于判断当前操作系统平台

# 启动命令
# D:/桌面/uni-/WowPic/WowPicServer/venv/Scripts/python.exe main.py
# .\venv\Scripts\activate

# 初始化colorama支持Windows彩色输出
colorama.init()

# 自定义静态文件类，添加缓存头
class CachedStaticFiles(StaticFiles):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    async def get_response(self, path: str, scope):
        response = await super().get_response(path, scope)

        # 为静态文件添加缓存头
        if hasattr(response, 'headers'):
            # 设置缓存时间为7天
            response.headers["Cache-Control"] = "public, max-age=604800"
            # 添加ETag支持
            response.headers["ETag"] = f'"{hash(path)}"'
            # 允许浏览器缓存
            response.headers["Expires"] = "Thu, 31 Dec 2037 23:55:55 GMT"

        return response

# 彩色日志配置
class ColoredFormatter(logging.Formatter):
    COLORS = {
        'DEBUG': Fore.CYAN,     # 青色
        'INFO': Fore.GREEN,     # 绿色
        'WARNING': Fore.YELLOW, # 黄色
        'ERROR': Fore.RED,      # 红色
        'CRITICAL': Fore.MAGENTA, # 紫色
    }
    
    def format(self, record):
        log_color = self.COLORS.get(record.levelname, '')
        # 格式化日志消息
        formatted = super().format(record)
        # 给整行日志着色
        return f"{log_color}{formatted}{Style.RESET_ALL}"

# 配置日志
handler = logging.StreamHandler()
handler.setFormatter(ColoredFormatter('%(asctime)s [%(levelname)s] %(message)s', datefmt='%H:%M:%S'))

# 清除默认处理器并设置根日志器
logging.root.handlers.clear()
logging.root.addHandler(handler)
logging.root.setLevel(logging.INFO)

logger = logging.getLogger(__name__)

# 禁用SQLAlchemy详细日志
logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)

# 禁用uvicorn的访问日志，减少轮询噪音
logging.getLogger('uvicorn.access').setLevel(logging.WARNING)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    try:
        db_initializer.initialize_database()
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        raise
    
    yield
    
    # 关闭时清理（如果需要）
    logger.info("应用正在关闭...")

# 创建FastAPI应用
app = FastAPI(
    title="WowPic API",
    description="WowPic AI图片生成服务API",
    version="1.0.0",
    lifespan=lifespan
)

# 挂载静态资源目录，支持 Banner 和其他静态文件访问（带缓存）
app.mount("/static", CachedStaticFiles(directory="static"), name="static")

@app.get("/")
async def root():
    return {"message": "WowPic API服务正在运行", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "database": "connected"}

# 注册认证路由
from utils.auth import router as auth_router
app.include_router(auth_router)

# 注册Banner路由
from utils.banner import router as banner_router
app.include_router(banner_router)

# 注册上传路由
from utils.upload import router as upload_router
app.include_router(upload_router)

# 注册风格路由
from utils.style import router as style_router
app.include_router(style_router)

# 注册内容安全检测路由
from utils.content_security import router as security_router
app.include_router(security_router)

# 注册图片生成路由
from services.generation_router import router as generation_router
app.include_router(generation_router)

# 注册签到路由
from utils.checkin import router as checkin_router
app.include_router(checkin_router)

# 注册支付路由
from utils.pay import router as pay_router
app.include_router(pay_router)

# 注册订阅消息路由
from utils.notify import router as notify_router
app.include_router(notify_router)

# 后续其他模块的路由可以继续在这里添加
# from routers.another_module import router as another_router
# app.include_router(another_router)

if __name__ == "__main__":
    logger.info(f"🚀 启动WowPic API服务")
    logger.info(f"🌐 服务地址: http://{Config.APP_HOST}:{Config.APP_PORT}")
    # 调试模式统一启用热重载，不再区分平台
    reload_enabled = True  # 始终启用热重载
    logger.info(f"🔧 调试模式已启用 (reload={'ON' if reload_enabled else 'OFF'})")

    uvicorn.run(
        "main:app",
        host=Config.APP_HOST,
        port=Config.APP_PORT,
        reload=True,  # 启用热重载
        reload_dirs=["WowPicServer"]
    )

# 项目运行：
# 1. 复制 .env.example 为 .env 并配置数据库信息
# 2. 安装依赖: pip install -r requirements.txt
# 3. 运行: python main.py