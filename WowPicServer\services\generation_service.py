import asyncio
import importlib
import logging
import threading
import time
from datetime import datetime
from typing import Dict, Optional, List, Type

from sqlalchemy.orm import Session

from database.models import Generation, GenerationStatus, Style, User, GenerationImage
from services.styles.base_style import BaseStyle
from services.styles.universal_style import UniversalStyle
from services.styles.style_config import get_provider_for_style, is_style_supported
from services.providers import AihubmixProvider, TuziProvider
from services.providers.tuzi_chat_provider import TuziChatProvider
from database.connection import db_manager
from utils.download import download_multiple_images
from utils.content_security import check_image_security

logger = logging.getLogger(__name__)


class PollCounter:
    """轮询计数器，线程安全的任务轮询次数跟踪"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._counters: Dict[str, int] = {}
            self._last_access: Dict[str, float] = {}
            self._counter_lock = threading.Lock()
            self._cleanup_interval = 300  # 5分钟清理一次过期计数器
            self._task_timeout = 600  # 10分钟后清理不活跃的任务
            self._initialized = True

            # 启动清理线程
            self._start_cleanup_thread()

    def _start_cleanup_thread(self):
        """启动后台清理线程，定期清理过期的计数器"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self._cleanup_interval)
                    self._cleanup_expired_counters()
                except Exception as e:
                    logger.error(f"轮询计数器清理线程异常: {e}")

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    def _cleanup_expired_counters(self):
        """清理过期的计数器"""
        current_time = time.time()
        expired_tasks = []

        with self._counter_lock:
            for task_id, last_time in self._last_access.items():
                if current_time - last_time > self._task_timeout:
                    expired_tasks.append(task_id)

            for task_id in expired_tasks:
                if task_id in self._counters:
                    del self._counters[task_id]
                if task_id in self._last_access:
                    del self._last_access[task_id]

        if expired_tasks:
            logger.debug(f"清理了 {len(expired_tasks)} 个过期的轮询计数器")

    @classmethod
    def increment_and_get(cls, task_id: str) -> int:
        """增加指定任务的轮询计数并返回当前计数"""
        instance = cls()
        current_time = time.time()

        with instance._counter_lock:
            # 增加计数
            if task_id not in instance._counters:
                instance._counters[task_id] = 0

            instance._counters[task_id] += 1
            instance._last_access[task_id] = current_time

            return instance._counters[task_id]

    @classmethod
    def reset_count(cls, task_id: str):
        """重置指定任务的轮询计数"""
        instance = cls()

        with instance._counter_lock:
            if task_id in instance._counters:
                del instance._counters[task_id]
            if task_id in instance._last_access:
                del instance._last_access[task_id]


class StyleFactory:
    """风格实现工厂，负责根据model_identifier创建对应的风格实现类"""

    _style_cache = {}  # 静态缓存，存储已实例化的风格处理器
    _provider_cache = {}  # 厂商实例缓存

    @classmethod
    def get_style_implementation(cls, model_identifier: str) -> BaseStyle:
        """
        根据model_identifier获取对应的风格实现实例

        Args:
            model_identifier: 风格标识符，如 'ghibli_v1', 'cat_travel_v1'

        Returns:
            BaseStyle: 风格实现类实例
        """
        # 如果已经缓存，直接返回
        if model_identifier in cls._style_cache:
            return cls._style_cache[model_identifier]

        # 首先尝试使用新的厂商架构
        if is_style_supported(model_identifier):
            try:
                # 获取对应的厂商
                provider_name = get_provider_for_style(model_identifier)
                if not provider_name:
                    raise ValueError(f"未找到风格 {model_identifier} 对应的厂商")

                # 获取厂商实例
                provider = cls._get_provider(provider_name)

                # 创建通用风格实现
                instance = UniversalStyle(provider, model_identifier)

                # 缓存实例
                cls._style_cache[model_identifier] = instance
                logger.info(f"使用新架构加载风格: {model_identifier} -> {provider_name}")
                return instance

            except Exception as e:
                logger.warning(f"新架构加载风格失败，尝试旧方式: {e}")

        # 回退到旧的加载方式（向后兼容）
        module_name = f"services.styles.{model_identifier}"
        class_name = ''.join(part.capitalize() for part in model_identifier.split('_')) + "Style"

        try:
            module = importlib.import_module(module_name)
            style_class = getattr(module, class_name)
            instance = style_class()

            # 缓存实例
            cls._style_cache[model_identifier] = instance
            logger.info(f"使用旧架构加载风格: {model_identifier}")
            return instance
        except (ImportError, AttributeError) as e:
            logger.error(f"无法加载风格处理器 {module_name}.{class_name}: {e}")
            raise ValueError(f"风格 '{model_identifier}' 未实现或无法加载: {e}")

    @classmethod
    def _get_provider(cls, provider_name: str):
        """获取厂商实例"""
        if provider_name not in cls._provider_cache:
            if provider_name == 'aihubmix':
                cls._provider_cache[provider_name] = AihubmixProvider()
            elif provider_name == 'tuzi_chat':
                cls._provider_cache[provider_name] = TuziChatProvider()
            elif provider_name == 'tuzi_edit' or provider_name == 'tuzi':
                cls._provider_cache[provider_name] = TuziProvider()
            else:
                raise ValueError(f"未知的厂商: {provider_name}")

        return cls._provider_cache[provider_name]


class GenerationService:
    """图片生成业务逻辑封装"""

    def __init__(self, db: Session):
        self.db = db
        self._background_tasks = {}

    async def generate_image(self, generation: Generation) -> List[str]:
        """根据 Generation 记录生成图片，返回图片 URL 列表"""
        style = self.db.query(Style).filter(Style.id == generation.style_id).first()
        if not style:
            logger.error(f"用户ID{generation.user_id} - 风格ID{generation.style_id} - 风格不存在")
            raise ValueError(f"风格 {generation.style_id} 不存在")

        # 获取风格处理器 - 使用StyleFactory替代旧方法
        handler = StyleFactory.get_style_implementation(style.model_identifier)

        # 准备生成参数
        # 获取所有源图片URL
        source_image_urls = [img.image_url for img in generation.source_images] if generation.source_images else []

        # 直接使用存储的完整提示词，不进行任何修改或参数提取
        prompt_text = generation.prompt or ""

        params = {
            "prompt": prompt_text,  # 直接使用存储的完整提示词
            "source_image_urls": source_image_urls,  # 传递所有源图片URL列表
            # 注意：不传递ratio和n参数，让AI厂商直接处理完整提示词
        }

        # 获取远程图片URL列表
        remote_image_urls = await handler.generate(params)

        # 并行下载图片到本地存储
        local_image_urls = await download_multiple_images(remote_image_urls, generation.user_id)

        # 成功汇总日志
        logger.info(f"用户ID{generation.user_id} - 风格ID{generation.style_id}({style.name}) - 生成图片{len(local_image_urls)}张 - 消耗{generation.cost}哇图币")

        return local_image_urls

    def generate_image_background(self, generation: Generation):
        """在后台线程中处理图片生成，不阻塞API响应"""
        # 创建新的数据库会话（每个线程需要独立会话）
        thread = threading.Thread(
            target=self._background_generation_thread,
            args=(generation.id,)
        )
        thread.daemon = True  # 设置为守护线程，随主进程退出
        thread.start()
        
        # 保存线程引用，便于管理
        self._background_tasks[generation.id] = thread
        return thread

    def _background_generation_thread(self, generation_id: int):
        """在后台线程中执行的生成任务"""
        # 创建独立的数据库会话
        db = next(db_manager.get_db())
        try:
            # 获取生成记录
            generation = db.query(Generation).filter(Generation.id == generation_id).first()
            if not generation:
                logger.error(f"找不到生成记录，ID: {generation_id}")
                return
                
            try:
                # 使用asyncio事件循环执行异步生成
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 将状态更新为处理中
                generation.status = GenerationStatus.PROCESSING
                db.commit()
                
                # 实例化服务并生成
                service = GenerationService(db)
                image_urls = loop.run_until_complete(service.generate_image(generation))
                
                # 进行内容安全检测（简化日志）
                security_check_count = 0
                for url in image_urls:
                    try:
                        check_result = loop.run_until_complete(check_image_security(url))
                        if check_result["success"]:
                            security_check_count += 1
                        else:
                            logger.error(f"❌ 内容安全检测提交失败: 用户ID{generation.user_id} - 风格ID{generation.style_id} - "
                                       f"任务ID{generation.task_id} - 图片URL: {url} - 错误: {check_result.get('message', '未知错误')}")
                    except Exception as e:
                        logger.error(f"❌ 内容安全检测异常: 用户ID{generation.user_id} - 风格ID{generation.style_id} - "
                                   f"任务ID{generation.task_id} - 图片URL: {url} - 异常: {str(e)}")

                # 安全检测汇总日志
                if security_check_count > 0:
                    logger.info(f"用户ID{generation.user_id} - 风格ID{generation.style_id} - 任务ID{generation.task_id} - 内容安全检测通过")
                
                # 更新生成结果
                if not image_urls:
                    # 没有生成任何图片，标记为失败
                    generation.status = GenerationStatus.FAILED
                    generation.error_message = "未能生成任何图片"
                elif len(image_urls) < generation.images_count:
                    # 部分成功
                    generation.status = GenerationStatus.PARTIAL
                    logger.warning(f"用户ID{generation.user_id} - 风格ID{generation.style_id} - 部分生成成功: {len(image_urls)}/{generation.images_count}张")
                else:
                    # 完全成功
                    generation.status = GenerationStatus.SUCCESS

                # 更新为实际生成数量
                generation.images_count = len(image_urls)
                
                # 存储所有成功生成的图片URL
                for url in image_urls:
                    db.add(GenerationImage(
                        generation_id=generation.id, 
                        image_url=url
                    ))
                
                # 设置完成时间
                generation.completed_at = datetime.utcnow()
                db.commit()

                # 成功日志已在generate_image方法中输出，这里不重复
                
            except Exception as e:
                logger.exception(f"生成失败: {e}")
                generation.status = GenerationStatus.FAILED
                generation.error_message = str(e)
                generation.images_count = 0
                generation.completed_at = datetime.utcnow()
                
                # 返还哇图币
                try:
                    # 查询用户
                    user = db.query(User).filter(User.id == generation.user_id).first()
                    if user:
                        user.coins += generation.cost
                        # 写入返还流水
                        from database.models import CoinTransaction, CoinTransactionSource
                        db.add(
                            CoinTransaction(
                                user_id=user.id,
                                change=generation.cost,
                                balance=user.coins,
                                source=CoinTransactionSource.REFUND,
                                source_id=generation.id,
                                remark="后台生成失败返还",
                            )
                        )
                        logger.info(f"已返还用户 {user.id} {generation.cost} 哇图币，因生成失败")
                    else:
                        logger.error(f"无法返还哇图币：找不到用户 ID {generation.user_id}")
                except Exception as refund_error:
                    logger.error(f"返还哇图币失败: {refund_error}")
                
                db.commit()
        finally:
            db.close()
            # 清理任务引用
            if generation_id in self._background_tasks:
                del self._background_tasks[generation_id]