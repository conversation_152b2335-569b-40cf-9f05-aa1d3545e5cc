import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from config import Config
from utils.auth import get_current_user, get_db
from database.models import Style, User
from sqlalchemy import not_

logger = logging.getLogger(__name__)

# 隐藏在风格列表中的模型标识符集合，可按需扩展
HIDDEN_MODEL_IDENTIFIERS = {"generic_v1"}

router = APIRouter(
    prefix="/wowpic/styles",
    tags=["风格"],
)

@router.get("")
async def get_styles(popular_only: bool = False, db: Session = Depends(get_db)):
    """
    获取风格列表
    
    - popular_only: 是否只返回热门风格
    """
    # 过滤掉不希望在前端展示的风格（如通用自由创作）
    query = db.query(Style).filter(not_(Style.model_identifier.in_(HIDDEN_MODEL_IDENTIFIERS)))

    if popular_only:
        # 权重大于 0 的视为热门，并按权重从高到低排序，再按ID保证稳定性
        query = query.filter(Style.is_popular > 0).order_by(Style.is_popular.desc(), Style.id.asc())
    else:
        # 普通列表也按权重排序，方便前端展示时靠前
        query = query.order_by(Style.is_popular.desc(), Style.id.asc())
    
    styles = query.all()
    
    # 直接返回相对路径，交由前端拼接完整URL
    for style in styles:
        # 处理封面图URL
        if style.cover_image_url and not style.cover_image_url.startswith("/"):
            style.cover_image_url = "/" + style.cover_image_url.lstrip("./")
        
        # 处理示例图片URLs
        if style.example_images and isinstance(style.example_images, list):
            processed_urls = []
            for url in style.example_images:
                if url and isinstance(url, str):
                    if not url.startswith("/"):
                        url = "/" + url.lstrip("./")
                    processed_urls.append(url)
            style.example_images = processed_urls
    
    return styles

@router.get("/popular")
async def get_popular_styles(db: Session = Depends(get_db)):
    """获取热门风格列表"""
    return await get_styles(popular_only=True, db=db)

@router.get("/{style_id}")
async def get_style_by_id(
    style_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取指定ID的风格详情
    
    - 需要用户登录
    - 返回完整的风格定义，包括模板类型和变量
    """
    style = db.query(Style).filter(Style.id == style_id).first()
    
    if not style:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="风格不存在"
        )
    
    # 保持相对路径，交由前端拼接
    if style.cover_image_url and not style.cover_image_url.startswith("/"):
        style.cover_image_url = "/" + style.cover_image_url.lstrip("./")
    
    # 处理示例图片URLs
    if style.example_images and isinstance(style.example_images, list):
        processed_urls = []
        for url in style.example_images:
            if url and isinstance(url, str):
                if not url.startswith("/"):
                    url = "/" + url.lstrip("./")
                processed_urls.append(url)
        style.example_images = processed_urls
    
    return style 