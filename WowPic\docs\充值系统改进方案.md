# 哇图币获取系统改进方案

## 1. 整体设计思路

### 1.1 页面形式选择：独立页面 vs 弹窗

**推荐使用独立页面的原因：**

1. **更好的用户体验**
   - 避免弹窗被误关闭导致的操作中断
   - 提供更大的展示空间，便于信息展示和交互
   - 支持更复杂的动画效果和视觉设计

2. **功能扩展性**
   - 可以展示更多获取金币的方式（未来扩展）
   - 支持添加用户使用统计、历史记录等功能
   - 便于集成更多营销活动和推广内容

3. **平台一致性**
   - 与现有的生成页面、个人中心页面保持一致的导航体验
   - 符合用户对"商店"类功能的心理预期

### 1.2 页面架构设计

**页面路径：** `/pages/coins/coins`

**页面结构：**
```
┌─────────────────────────────┐
│        导航栏               │
│  ← 哇图币商店               │
├─────────────────────────────┤
│        用户状态区            │
│  💰 当前余额：XXX 哇图币      │
│  📺 今日已观看：X/6 次       │
├─────────────────────────────┤
│        获取方式区            │
│  [看广告获取] [充值购买]      │
├─────────────────────────────┤
│        详细信息区            │
│  使用说明、限制条件等         │
└─────────────────────────────┘
```

## 2. 广告激励系统设计

### 2.1 基础规则设定

**奖励机制：**
- 每次观看完整广告获得：20 哇图币
- 每日观看限制：6 次
- 重置时间：每日 00:00（服务器时间）

**风控策略：**
- 必须完整观看广告才能获得奖励
- 服务端记录用户每日观看次数
- 防止客户端时间篡改的服务端验证

### 2.2 用户体验流程

**完整观看流程：**
1. 用户点击"看广告获取金币"按钮
2. 检查今日剩余次数（前端+后端双重验证）
3. 调用微信小程序激励视频广告API
4. 用户观看完整广告（30秒左右）
5. 广告完成回调触发
6. 调用后端接口发放奖励
7. 更新页面显示的金币余额和剩余次数
8. 播放金币获得动画效果

**异常处理流程：**
- 广告加载失败：显示重试按钮，提示网络问题
- 广告被提前关闭：不发放奖励，提示需要看完整广告
- 达到每日上限：按钮置灰，显示明日可用时间
- 网络请求失败：本地缓存状态，下次打开时重试

## 3. 后端接口设计

### 3.1 新增接口规划

**1. 获取用户广告观看状态**
- 接口：`GET /wowpic/ads/status`
- 功能：返回用户今日已观看次数、剩余次数、下次重置时间
- 响应：`{today_watched: 3, remaining: 3, reset_time: "2024-01-02T00:00:00Z"}`

**2. 广告观看完成奖励**
- 接口：`POST /wowpic/ads/reward`
- 功能：验证并发放广告观看奖励
- 请求：需要包含广告完成的验证信息
- 响应：`{success: true, coins_awarded: 20, new_balance: 150}`

**3. 获取金币获取页面数据**
- 接口：`GET /wowpic/coins/page-data`
- 功能：一次性返回页面所需的所有数据
- 响应：包含用户余额、广告状态、充值选项等

### 3.2 数据库设计调整

**用户广告观看记录表：**
- 表名：`user_ad_watches`
- 字段：
  - `user_id`: 用户ID
  - `watch_date`: 观看日期（YYYY-MM-DD格式）
  - `watch_count`: 当日观看次数
  - `last_watch_time`: 最后观看时间
  - `created_at`: 创建时间
  - `updated_at`: 更新时间

**金币流水表扩展：**
- 在现有 `CoinTransaction` 表的 `source` 枚举中新增：`AD_REWARD`
- `remark` 字段记录：`"观看广告奖励 - 第X次"`

## 4. 前端页面实现

### 4.1 页面组件结构

**主要组件：**
1. **CoinsHeader**: 显示当前余额和今日观看状态
2. **AdRewardCard**: 广告观看卡片，包含按钮和说明
3. **RechargeCard**: 充值卡片（仅安卓显示）
4. **CoinsAnimation**: 金币获得动画组件

### 4.2 状态管理

**页面数据状态：**
```javascript
data() {
  return {
    userCoins: 0,           // 用户当前金币
    todayWatched: 0,        // 今日已观看次数
    maxWatchesPerDay: 6,    // 每日最大观看次数
    coinsPerWatch: 20,      // 每次观看获得金币
    isLoading: false,       // 加载状态
    isWatchingAd: false,    // 正在观看广告状态
    showAnimation: false,   // 显示获得金币动画
    rechargeOptions: []     // 充值选项（安卓用户）
  }
}
```

### 4.3 微信广告集成

**广告管理器封装：**
- 创建 `utils/adManager.js` 工具类
- 封装广告加载、显示、回调处理逻辑
- 统一处理各种异常情况

**关键实现点：**
1. 广告预加载：页面进入时预先加载广告
2. 回调验证：确保广告完整播放才触发奖励
3. 错误重试：广告加载失败时的重试机制
4. 状态同步：广告完成后及时更新页面状态

## 5. 用户引导和交互设计

### 5.1 入口改造

**个人中心页面调整：**
- 将原有的充值按钮改为"获取金币"
- 添加小红点提示（当用户有剩余观看次数时）
- 点击后跳转到新的金币获取页面

**首页快捷入口：**
- 在首页适当位置添加"免费获取金币"入口
- 使用醒目的视觉设计吸引用户注意

### 5.2 用户教育

**首次使用引导：**
- 新用户首次进入页面时显示功能介绍
- 突出"免费"、"每日6次"等关键信息
- 提供一键体验按钮

**持续使用激励：**
- 显示今日剩余次数，营造稀缺感
- 在接近每日上限时给予鼓励提示
- 次日重置时发送提醒（如果有推送权限）

## 6. 平台差异化处理

### 6.1 iOS用户体验

**功能限制：**
- 隐藏所有充值相关功能
- 仅显示广告观看获取方式
- 在页面说明中提及"当前仅支持观看广告获取"

**体验优化：**
- 强化广告获取的价值感
- 提供更多免费获取的提示和技巧
- 通过设计让用户感受到平等待遇

### 6.2 安卓用户体验

**双重选择：**
- 同时显示广告观看和充值购买选项
- 通过视觉设计引导用户优先尝试免费方式
- 在用户多次使用广告后适时推荐充值

**转化引导：**
- 统计用户广告观看行为
- 对高频用户推荐充值套餐
- 提供充值优惠券等转化激励

## 7. 数据统计和分析

### 7.1 关键指标定义

**用户行为指标：**
- 页面访问次数和停留时间
- 广告观看完成率
- 每日活跃观看用户数
- 用户观看次数分布

**业务效果指标：**
- 广告收入 vs 充值收入比例
- 用户留存率变化
- 金币消耗和获取的平衡性
- iOS用户活跃度提升情况

### 7.2 数据收集方案

**前端埋点：**
- 页面进入、离开事件
- 按钮点击事件
- 广告加载、播放、完成事件
- 异常情况记录

**后端统计：**
- 接口调用频次和成功率
- 用户行为路径分析
- 收入数据统计
- 异常情况监控

## 8. 上线计划和风险控制

### 8.1 分阶段上线

**第一阶段：基础功能**
- 完成页面开发和基础广告集成
- 小范围内测，验证核心流程
- 收集用户反馈，优化体验

**第二阶段：功能完善**
- 添加数据统计和监控
- 优化异常处理和用户引导
- 扩大测试范围

**第三阶段：全量发布**
- 完成所有功能测试
- 准备运营活动和用户教育材料
- 正式发布并监控数据

### 8.2 风险控制措施

**技术风险：**
- 广告SDK稳定性监控
- 接口限流和异常处理
- 数据一致性保证

**业务风险：**
- 用户行为异常检测
- 收入影响评估
- 用户反馈快速响应机制

**合规风险：**
- 广告内容审核
- 用户隐私保护
- 平台政策合规性检查

## 9. 后续优化方向

### 9.1 功能扩展

**社交化元素：**
- 好友间金币赠送
- 观看广告排行榜
- 分享获得额外奖励

**游戏化设计：**
- 连续观看奖励
- 成就系统
- 等级特权

### 9.2 商业化优化

**精准推荐：**
- 基于用户行为的个性化推荐
- 动态调整奖励机制
- A/B测试不同策略效果

**多元化变现：**
- 品牌广告合作
- 会员订阅模式
- 增值服务销售

这个方案既解决了iOS用户无法充值的问题，又通过广告变现开辟了新的收入渠道，同时保持了良好的用户体验和商业价值平衡。
